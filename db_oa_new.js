var db = {};
var mysql = require('mysql');
var pool = mysql.createPool({
    connectionLimit: 10,
    host: 'rm-j6c95u8ak3n7w3cc9wo.mysql.rds.aliyuncs.com',
    user: 'root',
    password: '5RGbetad^&CVDAZ',
    database: 'eteams'
});

db.query = function(sql, params) {

    return new Promise((resolve, reject)=>{
        pool.getConnection(function(err, connection) {
            if(err){
                //console.log("建立连接失败");
            } else {
                connection.query(sql, params, function(err, rows, fields) {
                    connection.release();
                    if(err) {
                        console.log("查询失败." + err);
                    } else {
                        connection.destroy();
                        console.log(rows.length + 'rows');
                        resolve(rows);
                    }
                    
                })
            }
            //pool.end();
        });
    });

    
}


// db.query =  function(sql, params, callback) {

//     if (!sql) {
//         callback();
//         return;
//     }
//     pool.query(sql, params, function(err, rows, fields) {
//         if (err) {
//             console.log(err);
//             callback(err, null);
//             return;
//         };

//         callback(null, rows, fields);
//     });
// }
module.exports = db;