var db = {};
var mysql = require('mysql');
var pool = mysql.createPool({
    connectionLimit: 10,
    host: 'rdsqem2636h9e86625l1o.mysql.rds.aliyuncs.com',
    user: 'tesystem',
    password: 'BEHKabHU*@&c123*&^%bnmHJK2018',
    database: 'tesystem-2'
});

db.query = function(sql, params) {

    return new Promise((resolve, reject)=>{
        pool.getConnection(function(err, connection) {
            if(err){
                console.log("建立连接失败: " + err);
                reject(err);
            } else {
                connection.query(sql, params, function(err, rows, fields) {
                    connection.release();
                    if(err) {
                        console.log("查询失败." + err);
                        reject(err);
                    } else {
                        console.log(rows.length + 'rows');
                        resolve(rows);
                    }
                    
                })
            }
            //pool.end();
        });
    });

    
}


// db.query =  function(sql, params, callback) {

//     if (!sql) {
//         callback();
//         return;
//     }
//     pool.query(sql, params, function(err, rows, fields) {
//         if (err) {
//             console.log(err);
//             callback(err, null);
//             return;
//         };

//         callback(null, rows, fields);
//     });
// }
module.exports = db;