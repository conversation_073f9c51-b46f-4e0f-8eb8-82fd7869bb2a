
var tempID = 0;
function tree(data, tempID) {
    if (data.length > 0) {
        
        for(var i = 0; i < data.length; i++) {

            var insertKey =  data[i].insertID;
            var sqlStr = '';
            if (insertKey != undefined && insertKey != "" && tempID != "") {
                sqlStr += " " + insertKey + "='" + tempID + "',";
            }
            for (var key in data[i].sqlstr) {
                sqlStr += " " + key + "='" + data[i].sqlstr[key] + "',";
            }
            sqlStr = sqlStr.substring(0, sqlStr.length - 1) + " ";
            

            console.log('insert into ' + data[i].name + ' set ' + sqlStr);
            data[i].newID = Math.random();


            if (data[i].child) {
                tree(data[i].child, data[i].newID);
                
            }
        }
    }
    
}

var jsonObj = {
    "Action": "insert",
    "Tables": [
        {
            "name": "t1",
            "sqlstr": {
                "manager_name": "chris",
                "gender": "male",
                "type": "1"
            },
            "newID": "",
            "child": [
                {
                    "name": "t2",
                    "sqlstr": {
                        "manager_name": "john",
                        "gender": "male",
                        "type": "2"
                    },
                    "insertID": "t1_id",
                    "newID": "",
                    "child": [
                        {
                            "name": "t3",
                            "sqlstr": {
                                "manager_name": "john",
                                "gender": "male",
                                "type": "2"
                            },
                            "insertID": "t2_id",
                            "newID": "",
                            "child": [
                                {
                                    "name": "t4",
                                    "sqlstr": {
                                        "manager_name": "jenny",
                                        "gender": "femail",
                                        "type": "1"
                                    },
                                    "insertID": "t3_id"
                                }
                            ]
                        }
                    ]
                },
                {
                    "name": "t11",
                    "sqlstr": {
                        "manager_name": "john",
                        "gender": "male",
                        "type": "2"
                    },
                    "insertID": "t1_id"
                },
                {
                    "name": "t12",
                    "sqlstr": {
                        "manager_name": "tim",
                        "gender": "male",
                        "type": "3"
                    },
                    "insertID": "t1_id"
                }
            ]
        },
        {
            "name": "t9",
            "sqlstr": {
                "manager_name": "chris",
                "gender": "male",
                "type": "1"
            },
            "newID": "",
            "child": [
                {
                    "name": "t10",
                    "sqlstr": {
                        "manager_name": "john",
                        "gender": "male",
                        "type": "2"
                    },
                    "insertID": "t9_id"
                }
            ]
        }
    ]
};

tree(jsonObj.Tables, tempID);