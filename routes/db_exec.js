const e = require('express');
var express = require('express');
var router = express.Router();
var db = require('../db_connect.js');


/**
 * @swagger
 * /db_exec:
 *   post:
 *     tags:
 *       - DB Exec Helper
 *     description: Insert或Update数据库（Insert多表和子表以及update多表）
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: req_body
 *         description: Request object
 *         in: body
 *         required: true
 *     responses:
 *       200:
 *         description: success
 */
router.post('/', function(req, res, next) {
    var body = "";
    req.on('data', function (chunk) {
        body += chunk;  
    });
    req.on('end', async function () {
        try {
            body = JSON.parse(body);
            var jsonObj = body;
        } catch (err) {
            res.send(err.message);
        }

        if (body.Action == 'select') {
            res.send('Please call GET api to get data.');
        } else if (body.Action == 'update') {
            //var jsonStr = '{"Action":"update", "Tables":[{"name":"t1", "WhereID":"id", "sqlstr": { "id": "12", "manager_name":"chris", "gender":"male", "type":""}}, {"name":"t2", "WhereID":"id", "sqlstr": {"id": "23", "manager_name":"mary", "gender":"female", "type":"2"}}]}';
            //var jsonStr = body.Tables;
            var result_status = '';
            if (jsonObj.Tables != undefined) {
                
                for (var i = 0; i < jsonObj.Tables.length; i++) {
                    //iterate each table
                    var whereKey = "";
                    var whereValue = "";
                    var sqlStr = '';
                    let sqlValues = []
                    for (var key in jsonObj.Tables[i].sqlstr) {
                        if (jsonObj.Tables[i].sqlstr[key] != undefined) {
                            if (jsonObj.Tables[i].WhereID == key) {
                                whereKey = key;
                                whereValue = jsonObj.Tables[i].sqlstr[key];
                            } else {
                                sqlValues.push(jsonObj.Tables[i].sqlstr[key])
                                sqlStr += " " + key + "=?,";
                                //sqlStr += " " + key + "='" + jsonObj.Tables[i].sqlstr[key] + "',";
                            }
                        }
                    }
                    sqlStr = sqlStr.substring(0, sqlStr.length - 1) + " ";
                    if (whereKey != "") {
                        var whereStr = whereKey + "='" + whereValue + "'";
                        //const params = [jsonObj.Tables[i].name, sqlStr, whereStr];
                        //await db.query('update ' + jsonObj.Tables[i].name + ' set ' + sqlStr + ' where ' + whereStr).then(function (data) {
                        await db.query(`update ${jsonObj.Tables[i].name} set ${sqlStr} where ${whereStr} `, sqlValues).then(function (data) {
                            console.log('>>' + JSON.stringify(data) + ' updated');
                            if (JSON.stringify(data).indexOf('error_exec') >= 0) {
                                result_status = 'ERROR';
                            } else {
                                result_status = 'SUCCESS';
                            }
                        }).catch(function (err) {
                            console.log(err);
                        });
        
                        console.log('update ' + jsonObj.Tables[i].name + ' set ' + sqlStr + ' where ' + whereStr);
                    } else {
                        // no id defined
                        result_status = 'ERROR';
                        console.log('no id, exit');
                    }
                }
            }
            res.send('update ' + result_status);
            
        } else if (body.Action == 'insert') {

            if (jsonObj.Tables != undefined) {

                var tempID = 0;
                var result_status = await tree(jsonObj.Tables, tempID);
                
            }
            res.send('insert ' + result_status);
            
        } else if (body.Action == 'delete') {
            // update the is_valid as 0
            //var jsonStr = '{"Action":"update", "Tables":[{"name":"t1", "WhereID":"id", "sqlstr": { "id": "12", "manager_name":"chris", "gender":"male", "type":""}}, {"name":"t2", "WhereID":"id", "sqlstr": {"id": "23", "manager_name":"mary", "gender":"female", "type":"2"}}]}';
            //var jsonStr = body.Tables;
            var result_status = '';
            if (jsonObj.Tables != undefined) {
                for (var i = 0; i < jsonObj.Tables.length; i++) {
                    //iterate each table
                    var whereKey = "";
                    var whereValue = "";
                    var sqlStr = '';
                    for (var key in jsonObj.Tables[i].sqlstr) {
                        if (jsonObj.Tables[i].sqlstr[key] != undefined) {
                            if (jsonObj.Tables[i].WhereID == key) {
                                whereKey = key;
                                whereValue = jsonObj.Tables[i].sqlstr[key];
                            } else {
                                sqlStr += " " + key + "='" + jsonObj.Tables[i].sqlstr[key] + "',";
                            }
                        }
                    }
                    sqlStr = sqlStr.substring(0, sqlStr.length - 1) + " ";
                    if (whereKey != "") {
                        var whereStr = whereKey + "='" + whereValue + "'";
                        const params = [jsonObj.Tables[i].name, sqlStr, whereStr];
                        await db.query('update ' + jsonObj.Tables[i].name + " set is_valid = '0' where " + whereStr).then(function (data) {
                            console.log('>>' + JSON.stringify(data) + ' updated');
                            if (JSON.stringify(data).indexOf('error_exec') >= 0) {
                                result_status = 'ERROR';
                            } else {
                                result_status = 'SUCCESS';
                            }
                        }).catch(function (err) {
                            console.log(err);
                        });
        
                        console.log('update ' + jsonObj.Tables[i].name + " set is_valid = '0' where " + whereStr);
                    } else {
                        // no id defined
                        result_status = 'ERROR';
                        console.log('no id, exit');
                    }
                }
            }
            res.send('delete ' + result_status);
        } else {
            res.send('unknown request type.')
        }
    });
});


async function tree(data, tempID) {

    if (data.length > 0) { 
        for(var i = 0; i < data.length; i++) {
            console.log('counter: ' + i);
            var insertKey =  data[i].insertID;
            var sqlStr = '';
            var KeyID = null;
            let sqlValues = []
            if (insertKey != undefined && insertKey != "" && tempID != "") {
                //sqlStr += " " + insertKey + "='" + tempID + "',";
                sqlStr += " " + insertKey + "=?,";
                sqlValues.push(tempID)
            }
            for (var key in data[i].sqlstr) {
                // sqlStr += " " + key + "='" + data[i].sqlstr[key] + "',";
                sqlStr += " " + key + "=?,";
                sqlValues.push(data[i].sqlstr[key])
            }
            sqlStr = sqlStr.substring(0, sqlStr.length - 1) + " ";
            
            console.log('exec: ' + 'insert into ' + data[i].name + ' set ' + sqlStr);
            var db_status = '';
            // await db.query('insert into ' + data[i].name + ' set ' + sqlStr).then(function (data) {
            await db.query(`insert into ${data[i].name} set ${sqlStr} `, sqlValues).then(function (data) {
                console.log('>>' + JSON.stringify(data) + ' updated');
                if (JSON.stringify(data).indexOf('error_exec') >= 0) {
                    console.log('return=> ERROR');
                    db_status = 'ERROR';
                } else {
                    db_status = 'SUCCESS';
                    KeyID = data.insertId;
                }
            }).catch(function (err) {
                console.log(err);
            });
            console.log("inserted return id: " + KeyID);

            if (data[i].child) {
                console.log('run child');
                await tree(data[i].child, KeyID);  
            }
        }
        return db_status;
    } else {
        return 'ERROR';
    }
}

module.exports = router;