
var tempID = 0;
function tree(data, tempID) {
    if (data.length > 0) {
        
        for(var i = 0; i < data.length; i++) {

            var insertKey =  data[i].insertID;
            var sqlStr = '';
            if (insertKey != undefined && insertKey != "" && tempID != "") {
                sqlStr += " " + insertKey + "='" + tempID + "',";
            }
            for (var key in data[i].sqlstr) {
                sqlStr += " " + key + "='" + data[i].sqlstr[key] + "',";
            }
            sqlStr = sqlStr.substring(0, sqlStr.length - 1) + " ";
            

            console.log('insert into ' + data[i].name + ' set ' + sqlStr);
            data[i].newID = Math.random();


            if (data[i].child) {
                tree(data[i].child, data[i].newID);
                
            }
        }
    }
    
}

// sample json
var jsonObj = {
    "Action": "insert",
    "Tables": [
        {
            "name": "t_request_form",
            "sqlstr": {
                "customer_id": "123",
                "name": "Request Form Name",
                "teHoursRemaining": "12",
                "month_date": "10",
                "status": "2",
                "note_for_tutor": "Here is the Note for Tutor...",
                "remark": "I am remark.",
                "gmt_create": "2021-10-11 21:39:00"
            },
            "child": [
                {
                    "name": "t_request_form_school",
                    "sqlstr": {
                        "gmt_create": "2021-10-11 21:39:00",
                        "name": "Request Form School Name1",
                        "test_date": "2021-12-01 00:00:00",
                        "text_content": "Here is the Text Content 1...",
                        "note": "I am Note 1.",
                        "documents": "/upload/1.doc"
                    },
                    "insertID": "request_form_id",
                    "child": [
                        {
                            "name": "t_request_form_subject",
                            "sqlstr": {
                                "gmt_create": "2021-10-11 21:40:00",
                                "name": "Request Form Subject Name1",
                                "subject_tuition_type": "1",
                                "require_nextmonth": "0",
                                "learning_objective": "This is the Learning Objective.1",
                                "level": "1",
                                "exam_board": "I am Exam Board1",
                                "text_book": "I am the Text Book1.",
                                "Topic": "Topic One",
                                "subject_note": "Subject Note One.",
                                "documents": "/upload/subject.doc"
                            },
                            "insertID": "request_form_school_id"
                        },
                        {
                            "name": "t_request_form_subject",
                            "sqlstr": {
                                "gmt_create": "2021-10-11 21:40:00",
                                "name": "Request Form Subject Name2",
                                "subject_tuition_type": "2",
                                "require_nextmonth": "1",
                                "learning_objective": "This is the Learning Objective.2",
                                "level": "2",
                                "exam_board": "I am Exam Board2",
                                "text_book": "I am the Text Book2.",
                                "Topic": "Topic Two",
                                "subject_note": "Subject Note Tow.",
                                "documents": "/upload/subject.doc2"
                            },
                            "insertID": "request_form_school_id"
                        }
                    ]
                },
                {
                    "name": "t_request_form_school",
                    "sqlstr": {
                        "gmt_create": "2021-10-11 21:40:00",
                        "name": "Request Form School Name2",
                        "test_date": "2021-12-01 12:00:00",
                        "text_content": "Here is the Text Content 2...",
                        "note": "I am Note 2.",
                        "documents": "/upload/2.doc"
                    },
                    "insertID": "request_form_id",
                    "child": [
                        {
                            "name": "t_request_form_subject",
                            "sqlstr": {
                                "gmt_create": "2021-10-11 21:40:00",
                                "name": "Request Form Subject Name3",
                                "subject_tuition_type": "3",
                                "require_nextmonth": "3",
                                "learning_objective": "This is the Learning Objective.3",
                                "level": "3",
                                "exam_board": "I am Exam Board3",
                                "text_book": "I am the Text Book3.",
                                "Topic": "Topic Three",
                                "subject_note": "Subject Note Three.",
                                "documents": "/upload/subject.doc2"
                            },
                            "insertID": "request_form_school_id"
                        }
                    ]
                }
            ]
        }
    ]
};

tree(jsonObj.Tables, tempID);