var express = require('express');
var router = express.Router();

const SMSClient = require('@alicloud/sms-sdk');
const { json } = require('body-parser');

const accessKeyId = 'LTAIvGr6GUJ2GdUV'
const secretAccessKey = '8DX9PUGjtEmT8C7Pj3CUQgOnsQ6l0A'

const token = 't9WW51hDV!FwSRIR'

/**
 * @swagger
 * /sms:
 *   post:
 *     tags:
 *       - Aliyun SMS
 *     description: Aliyun SMS  Sample&#58; {"token"&#58;"abc123!@#", "phone"&#58;"13916605619", "sign"&#58;"SG名校指南", "template"&#58;"SMS_155570370", "template_param"&#58;{"code"&#58;"Hello"}}
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: req_body
 *         description: Request object
 *         in: body
 *         required: true
 *     responses:
 *       200:
 *         description: success
 */
router.post('/', function (req, res, next) {
  let body = ""
  let jsonObj = {}
  req.on('data', function (chunk) {
    body += chunk
  });
  req.on('end', async function () {
    try {
      body = JSON.parse(body);
      jsonObj = body;
    } catch (err) {
      res.send(err.message);
    }
    if(jsonObj.token != token) {
      console.log('token missing')
      res.send('token missing')
      return
    }
    let smsClient = new SMSClient({
      accessKeyId,
      secretAccessKey
    })
    try {
      smsClient.sendSMS({
        PhoneNumbers: jsonObj.phone, 
        SignName: jsonObj.sign,
        TemplateCode: jsonObj.template,
        TemplateParam: JSON.stringify(jsonObj.template_param)
      }).then(function (response) {
        let { Code } = response
        if (Code === 'OK') {
          console.log('sent')
          res.send('OK');
        } else {
          res.send('FAILED')
        }
      }, function (err) {
        console.log(err)
        res.send('FAILED')
      })
    } catch (e) {
      console.log(e)
      res.send('FAILED')
    }
  })
})

module.exports = router