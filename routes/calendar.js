var express = require('express');
var router = express.Router();
var qs = require('qs')
var axios = require('axios')
var circularJson = require('circular-json')

router.get('/', async function (req, res) {
  //get token
  const data = {
    "grant_type": "client_credentials",
    "client_id": "16478243-f969-4432-93f9-fabd246e259d",
    "client_secret": "****************************************",
    "scope": "https://graph.microsoft.com/.default"
  };
  //console.log(data);

  const options = {
    method: 'POST',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data: qs.stringify(data),
    url: 'https://login.microsoftonline.com/beedu.onmicrosoft.com/oauth2/v2.0/token'
  };
  console.log(options);
  var result = await axios(options);
  let token = ''
  token = result.data.access_token
  console.log(token)

  //get calendars events
  const options2 = {
    method: 'GET',
    headers: {
      'content-type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    url: `https://graph.microsoft.com/v1.0/users/<EMAIL>/events?$filter=start/dateTime ge '2022-12-05T00:00' and start/dateTime lt '2022-12-05T23:59'`
  }
  let result2 = await axios(options2)
  console.log(result2.data.value)
  let eventList = []
  eventList = result2.data.value

  let events = []
  // let event = {}

  //format the events
  if (eventList.length > 0) {
    for (let i = 0; i < eventList.length; i++) {
      let eventitem = {}
      eventitem.id = eventList[i].id
      eventitem.location = eventList[i].location.displayName
      eventitem.subject = eventList[i].subject
      eventitem.start = eventList[i].start.dateTime
      eventitem.end = eventList[i].end.dateTime
      events.push(eventitem)
    }

  }

  res.json(events);

});

module.exports = router
