
const pa11y = require('pa11y')

var express = require('express');
var router = express.Router();

router.get('/', async (req, res) => {
    if (!req.query.url) {
      res.status(400).json({ error: 'url is required' })
    } else {
		try {
			console.log('start here:' + req.query.url)
			const results = await pa11y(req.query.url)
			res.status(200).json(results)
		} catch (e) {
			res.status(400).json({ error: e.message })
		}
      
    }
})
module.exports = router;