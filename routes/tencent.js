const e = require('express');
var express = require('express');
var router = express.Router();
const axios = require('axios')
const jose = require('node-jose');
const jwt = require('jsonwebtoken')
var db = require('../db_te.js');

/**
 * @swagger
 * /tencent:
 *   post:
 *     tags:
 *       - Tencent
 *     description: Insert或Update数据库（Insert多表和子表以及update多表）
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: req_body
 *         description: Request object
 *         in: body
 *         required: true
 *     responses:
 *       200:
 *         description: success
 */
router.post('/', function(req, res, next) {
    var body = "";
    req.on('data', function (chunk) {
        body += chunk;  
    });
    req.on('end', async function () {
        try {
            body = JSON.parse(body);
            var jsonObj = body;
            
        } catch (err) {
            res.send(err.message);
        }

        if (!body.values) {
            return
        }
        let username = body.values.username
        let displayName = body.values.displayName
        let phoneNum = body.values.phoneNum
        let primaryMail = body.values.primaryMail
        let deptId = body.values.deptId
        let zohoid = body.values.username //zohoid is username
        let name = body.values.name
        let type = body.values.type 

        let token = ''
        // get token
        payload = {
            "aud": "contacts",
            "iss": "d6a1cabc-0da2-41c6-97f2-8b516f271e5a", //填写ServiceAccount对应的clientId
            "account_type": "serviceAccount",
            "iat": Math.floor(Date.now() / 1000),
            "exp": Math.floor((Date.now() + 3600000) / 1000)
        };

        let result = await getPrivateKey(serviceAccount.privateKey)
        token = generateTokenTencent(payload, result)

        const Headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        };

        try {
            let data = JSON.stringify({
                'values': {
                    username,
                    displayName,
                    phoneNum,
                    primaryMail,
                    deptId,
                }
            })

            let config = {
                method: 'post',
                maxBodyLength: Infinity,
                url: 'https://meeting2291098842678-admin.id.meeting.qq.com/contacts/api/v1/users',
                headers: { 
                'Content-Type': 'application/json', 
                'Authorization': 'Bearer ' + token
                },
                data : data
            }

            axios.request(config)
            .then(async (response) => {
                //console.log(JSON.stringify(response.data));
                //console.log('fuck', response.errorMsg)
                if(response.data.id) {

                    // add record to db
                    let db_status = ''
                    let KeyID = ''
                    console.log(`insert into tencentaccount (zohoid, userid, type, name) values('${zohoid}', '${response.data.id}', '${type}', '${name}')`)
                    await db.query(`insert into tencentaccount (zohoid, userid, type, name) values('${zohoid}', '${response.data.id}', '${type}', '${name}')`).then(function (data) {
                        console.log('>>' + JSON.stringify(data) + ' updated');
                        if (JSON.stringify(data).indexOf('error_exec') >= 0) {
                            console.log('return=> ERROR');
                            db_status = 'ERROR';
                        } else {
                            db_status = 'SUCCESS';
                            KeyID = data.insertId;
                        }
        
                    }).catch(function (err) {
                    });

                    res.send(response.data.id)
                } else {
                    res.send('error')
                }
                
            })
            .catch(function (error) {
                if (error.response) {
                    // The request was made and the server responded with a status code
                    // that falls out of the range of 2xx
                    console.log(error.response.data);
                    console.log(error.response.status);
                    if (error.response.data.errorCode == '*********')
                    {
                        let msg = error.response.data.errorMsg + ' ' + JSON.stringify(error.response.data.details)
                        res.send('account duplicated' + msg)
                    } else {
                        res.send(error.response.data)
                    }
                    
                    //console.log(error.response.headers);
                } else if (error.request) {
                    // The request was made but no response was received
                    // `error.request` is an instance of XMLHttpRequest in the browser 
                    // and an instance of http.ClientRequest in node.js
                    console.log(error.request);
                } else {
                    // Something happened in setting up the request that triggered an Error
                    console.log('Error', error.message);
                }
                //res.send('err')
            })

        } catch (err) {
            res.send(err.message);
        }

    });
});

/**
 * @swagger
 * /tencent/del:
 *   post:
 *     tags:
 *       - Tencent Del
 *     description: Insert或Update数据库（Insert多表和子表以及update多表）
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: req_body
 *         description: Request object
 *         in: body
 *         required: true
 *     responses:
 *       200:
 *         description: success
 */
router.post('/del', function(req, res, next) {
    
    var body = "";
    req.on('data', function (chunk) {
        body += chunk;  
    });
    req.on('end', async function () {
        try {
            body = JSON.parse(body);
            var jsonObj = body;
        } catch (err) {
            res.send(err.message);
        }
        if (!body) {
            return
        }

        let tencentid = body.userid
        let type = body.type

        let token = ''
        // get token
        payload = {
            "aud": "contacts",
            "iss": "d6a1cabc-0da2-41c6-97f2-8b516f271e5a", //填写ServiceAccount对应的clientId
            "account_type": "serviceAccount",
            "iat": Math.floor(Date.now() / 1000),
            "exp": Math.floor((Date.now() + 3600000) / 1000)
        };

        let result = await getPrivateKey(serviceAccount.privateKey)
        token = generateTokenTencent(payload, result)

        const Headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        };

        try {

            let config = {
                method: 'delete',
                maxBodyLength: Infinity,
                url: 'https://meeting2291098842678-admin.id.meeting.qq.com/contacts/api/v1/users/' + tencentid,
                headers: { 
                'Content-Type': 'application/json', 
                'Authorization': 'Bearer ' + token
                }
            }
            axios.request(config)
            .then(async (response) => {
                //console.log(JSON.stringify(response.data));
                //console.log('fuck', response.errorMsg)

                // soft delete record to db
                let db_status = ''
                console.log("update tencentaccount set del = '1' where userid='" + tencentid + "'")
                await db.query("update tencentaccount set del = '1' where userid='" + tencentid + "'").then(function (data) {
                    console.log('>>' + JSON.stringify(data) + ' updated');
                    if (JSON.stringify(data).indexOf('error_exec') >= 0) {
                        console.log('return=> ERROR');
                        db_status = 'ERROR';
                    } else {
                        db_status = 'SUCCESS';
                    }
    
                }).catch(function (err) {
                });

                // update lanmao tables
                if (type == 't' || type =='te') {
                    await db.query("update user set tencentid = '' where tencentid='" + tencentid + "'").then(function (data) {
                        console.log('>>' + JSON.stringify(data) + ' updated');
                        if (JSON.stringify(data).indexOf('error_exec') >= 0) {
                            console.log('return=> ERROR');
                            db_status = 'ERROR';
                        } else {
                            db_status = 'SUCCESS';
                        }
        
                    }).catch(function (err) {
                    });
                } else if (type == 's') {
                    await db.query("update student set tencentid = '' where tencentid='" + tencentid + "'").then(function (data) {
                        console.log('>>' + JSON.stringify(data) + ' updated');
                        if (JSON.stringify(data).indexOf('error_exec') >= 0) {
                            console.log('return=> ERROR');
                            db_status = 'ERROR';
                        } else {
                            db_status = 'SUCCESS';
                        }
        
                    }).catch(function (err) {
                    });
                }
                

                res.send(db_status)
                
            })
            .catch(function (error) {
                res.send('error')
            })

        } catch (err) {
            res.send(err.message);
        }

    });
});

async function getPrivateKey(privateKey) {
	const keystore = jose.JWK.createKeyStore();
	await keystore.add(privateKey, 'pem');
	const key = keystore.get({ kty: 'RSA', use: 'sig' });
	console.log(key)
	return key.toPEM(true);
}


const generateTokenTencent = (payload, privateKey) => {
	return jwt.sign(payload, privateKey, { algorithm: 'RS256', header })
}

const serviceAccount = {
	"clientId": "d6a1cabc-0da2-41c6-97f2-8b516f271e5a",
	"privateKey": {
		"p": "-ZlBOP445kSFQExfDF6DhIAPUmrJ7XRxCLRSTIH2NLZR42DERM3x2btT2umpA7RfKPYYTNaC0SZTEmS2UlXQ2uUpH32cfmE3_UtHJtJUFU4acx_YkQUq8xGkuZDrgX_c2glDk8nOta1wGe7BupDw7cReNriQE8TcqcjMBPxPQD0",
		"kty": "RSA",
		"q": "wweNeqJ33QUI8iMNN3y89PJCBAGaArbWJSCZysPcuu8VrOUB_SQWF_I3OrLBHszIhrwFK2yemjOrpIdzCNFffvtwABQz3CR_3tErHjAZOZmp8Td4WKaWFZPhncYVadhaNCUsH8ZQBI6i_KVUsLLhSE7NAfUj9LsqTte3_aj-cTk",
		"d": "p96bEkYWczV0VJR07szJFAq8IblYMRo_cfacwwTLnhsNfX57odcReE7reQGJU58kO1BjzAGslB2O1NlEg0XQFiCxeE3h23Ys7ZU7qboZTNgAsrfDdklgfMfJRRrzb4RUnihen9YckEEB1s3HKYvq6kuv27uWxORs6Upnbi8kwLGx90SbYBntavAs13vUPnxyInt45NA36nFsAK4oPKTAjul4rA304GzriZXG04lzrafhftjiyv9pzLiaPXKbfiDgIgZ8d4iU8WuPO-eWTz2fSc9ZkPMbCuYxmCcgKBskzdj50w-Dw5woA8or1EHF8qiZOoQVY0D6IJAaDhonaCXJIQ",
		"e": "AQAB",
		"use": "sig",
		"kid": "755b29f7-ac31-4eec-97c4-d08fcd4d08cc",
		"qi": "elGUyKvJzKaeXMVy-w43L89JCaP5UOII4aL9tUcRiPqyyxVFK-b5pC-rlIZlQaxfLSjCH8Ng8b9gfNGKHO_JGpozy0yiydt7cyVJ5xTwyn3zdgj0Y9ltT8OMLWyvV-aelWeZcxIU564wJBBzyOoZP04W3fVvpP9wzbDJL4vBQuI",
		"dp": "T-MtbQBxVcI4pAvNoDfgAFi8jrR_TPEn_JE6hiJSmyx_qTcfmqYP8pBylm6TGgxxKLsbq_w8gpSxogF6GYA-CNGwB9Q8cnbLZNbjPxmyr97_uDlJJ6Fvzbyn7Hzl49z44xB0AKlm799DMgmpF_2sH23qebk45BruRalvI2leNEE",
		"dq": "X4HtWOcDR-tVTC66JDgj2NhiV8_o-fdLu3bRmah9CdZXiQnFBZCMeUGWplovlMO4V7Lv3SoeKWmwXqwuO109JNRBLui11NLTA-zu7qOjVr_Xp1cAynkZ-osm67q7ddALrImfqBfXy8OGMa9NYBLZnC1q29COwSR_PYkKg2Ak6pE",
		"n": "vicZ0CyRnSSTtJ_PKmsakvGLlo6ZU6akIX7IwYaSP86i4C6Nh6s8wv1QMETk4tkDZ5SilUBwhO1xS9plY9hH8gV52zXM-K-lC3TN68lyrd65ZfFYn2GpOTMHwqyy0m3QF2t-JQ5DOMD2MImL0Cx6qZxADKb2xIzxj5nGXc5k9lByRDW1CJDt7XZbEfjEDyXXJw-Gt0LiamOZ0ktvHG5K8IOVV1bAGGs5sTHv9UpW_sd7DcrOTxQB1hVGoxY_-_cwg8itgnuFWVqbkMwr7xYYYtWPvEcISjVF3ASmbHAscxpeXJC7Jb_1IbMh5hR5GTRvSEjKj_0SzZ6swxCN7YY6lQ"
	}
}

const header = {
	"alg": "RS256",
	"typ": "JWT",
	"kid": "755b29f7-ac31-4eec-97c4-d08fcd4d08cc"
};

module.exports = router;