var express = require('express');
const mail = require('nodemailer');
var router = express.Router();

const SMSClient = require('@alicloud/sms-sdk');
const { json } = require('body-parser');

const accessKeyId = 'LTAIvGr6GUJ2GdUV'
const secretAccessKey = '8DX9PUGjtEmT8C7Pj3CUQgOnsQ6l0A'

const token = 't9WW51hDV!FwSRIR'

/**
 * @swagger
 * /sendmail:
 *   post:
 *     tags:
 *       - Send email
 *     description: Aliyun SMS  Sample&#58; {"token"&#58;"abc123!@#", "email"&#58;"<EMAIL>; <EMAIL>", "subject"&#58;"Car-pooling system email notification - TEST"}
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: req_body
 *         description: Request object
 *         in: body
 *         required: true
 *     responses:
 *       200:
 *         description: success
 */
router.post('/', function (req, res, next) {
  let body = ""
  let jsonObj = {}
  req.on('data', function (chunk) {
    body += chunk
  });
  req.on('end', async function () {
    try {
      body = JSON.parse(body);
      jsonObj = body;
    } catch (err) {
      res.send(err.message);
    }
    if (jsonObj.token != token) {
      console.log('token missing')
      res.send('token missing')
      return
    }

    let transport = null;
    let html = ''
    let options = {}
    if (jsonObj.type == 'obt') {
      transport = mail.createTransport({
        host: 'smtp.office365.com',
        port: '587',
        auth: { user: '<EMAIL>', pass: 'Ton52347' },
        secureConnection: false,
        tls: { ciphers: 'SSLv3' }
      });
      if (jsonObj.reportType == 'feedback') {//老师发布反馈，顾问收到邮件并翻译。顾问收到反馈
        html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/>Dear ${jsonObj.consultant}, <br/><br/> OBT tutor: ${jsonObj.teacher} has uploaded a report for ${jsonObj.student}'s ${jsonObj.course} scheduled for ${jsonObj.scheduledate}. Please log in to the system to view. <br/><br/>Click the access link: ${jsonObj.url}<br/></body>`
      }else if (jsonObj.reportType == 'feedbackStudent') {//顾问翻译好发布，学生和老师收到邮件。学生收到反馈
        html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/>Dear ${jsonObj.student}, <br/><br/> OBT tutor: ${jsonObj.teacher} has uploaded a report for your ${jsonObj.course} scheduled for ${jsonObj.scheduledate}. Please log in to the system to view.<br/><br/>Click the access link: ${jsonObj.url}<br/></body>`
      }else if (jsonObj.reportType == 'feedbackTeacher') {//顾问翻译好发布，学生和老师收到邮件。老师收到反馈
        html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/>Dear ${jsonObj.teacher}, <br/><br/> Your reports have been translated and published for ${jsonObj.student}'s ${jsonObj.course} scheduled for ${jsonObj.scheduledate}. Please log in to the system to view.<br/><br/>Click the access link: ${jsonObj.url}<br/></body>`
      } else if (jsonObj.reportType == 'homework') {//老师发布作业，顾问和学生收到邮件.顾问收到老师发布的作业
        html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/>Dear ${jsonObj.consultant}, <br/><br/> OBT tutor: ${jsonObj.teacher} has assigned homework for ${jsonObj.student}'s ${jsonObj.course} scheduled for ${jsonObj.scheduledate}. Please log in to the system to view.<br/><br/>Click the access link: ${jsonObj.url}<br/></body>`
      } else if (jsonObj.reportType == 'homework_updated') {//作业更新通知顾问
        html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/>Dear ${jsonObj.consultant}, <br/><br/> OBT tutor: ${jsonObj.teacher}'s homework has updated for ${jsonObj.student}'s ${jsonObj.course} scheduled for ${jsonObj.scheduledate}. Please log in to the system to view.<br/><br/>Click the access link: ${jsonObj.url}<br/></body>`
      } else if (jsonObj.reportType == 'homeworkStudent_assigned') {//学生收到老师发布的作业
        html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/>Dear ${jsonObj.student}, <br/><br/> OBT tutor: ${jsonObj.teacher} has assigned your homework for the ${jsonObj.course} scheduled for ${jsonObj.scheduledate}. Please log in to the system to view.<br/><br/>Click the access link: ${jsonObj.url}<br/></body>`
      } else if (jsonObj.reportType == 'homeworkTeacher_submitted') {//老师收到学生提交的作业
        html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/>Dear ${jsonObj.teacher}, <br/><br/> ${jsonObj.student} has submitted homework for ${jsonObj.course} scheduled for ${jsonObj.scheduledate}. Please log in to the system to view.<br/><br/>Click the access link: ${jsonObj.url}<br/></body>`
      } else if (jsonObj.reportType == 'homeworkStudent_corrected') {//学生收到老师批改的作业
        html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/>Dear ${jsonObj.student}, <br/><br/> OBT tutor: ${jsonObj.teacher} has corrected your homework for the ${jsonObj.course} scheduled for ${jsonObj.scheduledate}. Please log in to the system to view.<br/><br/>Click the access link: ${jsonObj.url}<br/></body>`
      } else if (jsonObj.reportType == 'obtform') {
        html = `
        <body style='font-family: Helvetica, Geneva, sans-serif'>
        <br/>
        Dear ${jsonObj.student},<br />
        <br />
        Thank you for booking a free consultation with Oxbridge Tutors. <br />
        <br />
        We will call you at ${jsonObj.phone} or add your WhatsApp in the next 1 working day to have a brief phone consultation in preparation of you chosen class. The number we will be calling from is +852 21297175. <br />
        <br />
        Please call or message us at +852 56613196 if you have any questions or would prefer us to contact you at a different number.<br />
        <br />
        Your provided information:<br />
        Student Name: ${jsonObj.student} <br />
        Contact Number: ${jsonObj.phone} <br />
        Student Age: ${jsonObj.age} <br />
        Subject Area: ${jsonObj.subjectName} <br />
        </body>`
      }
      
      options = {
        from: '<EMAIL>',
        to: jsonObj.email,
        bcc: jsonObj.bcc,
        subject: jsonObj.subject,
        html: html
      };
    } else if (jsonObj.type == 'meeting') {
      transport = mail.createTransport({
        host: 'smtp.mxhichina.com',
        secureConnection: true,
        port: 465,
        auth: {
          user: "<EMAIL>",
          pass: "BEHKabc123"
        }
      });
      let hostname = extractNameFromEmail(jsonObj.email)
      html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/><br/>Meeting scheduler:<br/><br/>${jsonObj.hostname} would like to convene a meeting sometime between ${jsonObj.startdate} and ${jsonObj.enddate}, please click the link below and then choose the earliest time slots when you are available.<br/><br/>${jsonObj.url}</body>`;

      options = {
        from: '"Meeting Scheduler" <EMAIL>',
        to: jsonObj.email,
        subject: jsonObj.subject,
        html: html
      };
    } else if (jsonObj.type == 'waisgc') {
      transport = mail.createTransport({
        host: 'smtp.mxhichina.com',
        secureConnection: true,
        port: 465,
        auth: {
          user: "<EMAIL>",
          pass: "BEHKabc123"
        }
      });
      html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/><b>Name of Child:  </b>${jsonObj.name}<br/><br/><b>Date of Birth:  </b>${jsonObj.birthday}<br/><br/><b>Contact Number:  </b>${jsonObj.tel}<br/><br/><b>Email Address:  </b>${jsonObj.emailAddress}</body>`;

      options = {
        from: '"Leads from https://summercamp.waisgc.com/" <EMAIL>',
        to: jsonObj.email,
        subject: jsonObj.subject,
        html: html,
        bcc: jsonObj.bcc
      };
    }  else {
      transport = mail.createTransport({
        host: 'smtp.mxhichina.com',
        secureConnection: true,
        port: 465,
        auth: {
          user: "<EMAIL>",
          pass: "BEHKabc123"
        }
      });
      html = `<body style='font-family: Helvetica, Geneva, sans-serif'><br/><br/>车辆行程单:<br/><br/></body>`;

      options = {
        from: '"订车系统" <EMAIL>',
        to: jsonObj.email,
        subject: jsonObj.subject,
        html: html
      };
    }

    let status = ''
    status = await sendEmail(transport, options);
    res.send(status)
  })
})

async function sendEmail(trans, op) {
  try {
    let result = await trans.sendMail(op);
    console.log(result)
    return 'success'
  } catch (err) {
    return 'error'
  }
}

function extractNameFromEmail(email) {
  // 验证邮箱地址格式是否正确
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return "Invalid email address";
  }

  // 提取邮箱地址中的用户名部分
  const username = email.split('@')[0];

  // 检查用户名中是否有点号来判断是否有姓和名
  if (username.includes('.')) {
    // 分割用户名中的姓和名，并加入空格和大写首字母
    const [firstName, lastName] = username.split('.');
    const formattedName = `${capitalizeFirstLetter(firstName)} ${capitalizeFirstLetter(lastName)}`;
    return formattedName;
  } else {
    // 没有点号，直接大写第一个字母并返回
    return capitalizeFirstLetter(username);
  }
}

// 首字母大写的辅助函数
function capitalizeFirstLetter(word) {
  return word.charAt(0).toUpperCase() + word.slice(1);
}

module.exports = router