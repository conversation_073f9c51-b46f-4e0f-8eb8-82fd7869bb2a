var express = require('express');
var router = express.Router();
var db = require('../db_connect.js');
var base64 = require('js-base64');


/**
 * @swagger
 * /onlinete:
 *   post:
 *     tags:
 *       - Receive 268 user ID
 *     description: Receive 268 user ID
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: req_body
 *         description: Request object
 *         in: body
 *         required: true
 *     responses:
 *       200:
 *         description: success
 */
router.post('/', function(req, res, next) {
    var body = "";
    req.on('data', function (chunk) {
        body += chunk;  
    });
    req.on('end', async function () {
        var isSuc = false;
        var msg = '';
        try {
            body = JSON.parse(body);
            var jsonObj = body;
        } catch (err) {
            msg = err.message;
        }
        
        if (jsonObj == undefined || jsonObj.userId == undefined || jsonObj.type == undefined) {
            msg = 'no params';
            console.log('no params');
            //res.send({'success': false, 'message': 'no params'});
        } else {
            var userId = jsonObj.userId;
            var type = jsonObj.type;
            var from = jsonObj.from;
            
            var bec_id = '';
            var zoho_user_id = '';
            var url = '';

            var sql = '';

            if (from == 'lm') {
                // query for 268
                sql = "SELECT id, online_te_id, zoho_user_id from t_customer where zoho_user_id ='" + userId + "'";
            } else {
                sql = "SELECT id, online_te_id, zoho_user_id from t_customer where online_te_id ='" + userId + "'";
            }

            await db.query(sql).then(function (rows) {
                console.log(rows);
                if (rows.length > 0) {
                    bec_id = rows[0].id;
                    zoho_user_id = rows[0].zoho_user_id == null ? '' : rows[0].zoho_user_id;
                    url = 'https://connect.be.co/#/' + type + '?jytfFDJyrtdjy=' + base64.encode(bec_id + '*beit2022*' + zoho_user_id);
                    console.log(bec_id + '*beit2022*' + zoho_user_id);
                }
                
            }).catch(function (err) {
                console.log(err);
            });

            isSuc = true;
            msg = '调用成功';
        }
        var result = {'success': isSuc, 'message': msg, 'url': url};
        res.send(result);
        
    });
});

module.exports = router;