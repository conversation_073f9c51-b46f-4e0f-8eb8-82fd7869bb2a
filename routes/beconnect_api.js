var express = require('express');
var router = express.Router();
var db = require('../db_connect.js');
var db_te = require('../db_te.js');
const pa11y = require('pa11y')
const axios = require('axios')
const qs = require('qs');
const { json } = require('body-parser');


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/te_class_new:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取TE系统排课数据。如果没有参数则默认：pageNum=1, pageSize=15, startDate=2020-09-01
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'department_id'
 *        in: 'query'
 *        description: '部门ID'
 *        type: 'integer'
 *      - name: 'showOther'
 *        in: 'query'
 *        description: '是否显示下属（0:否 1:是）'
 *        type: 'string'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'startDate'
 *        in: 'query'
 *        description: '开始日期yyyy-mm-dd'
 *        type: 'string'
 *      - name: 'ClassscheduleStatus'
 *        in: 'query'
 *        description: '状态'
 *        type: 'string'
 *      - name: 'ClassscheduleZone'
 *        in: 'query'
 *        description: '校区'
 *        type: 'string'
 *      - name: 'leaveState'
 *        in: 'query'
 *        description: '请假状态'
 *        type: 'string'
 *      - name: 'student_id'
 *        in: 'query'
 *        description: '学生ID'
 *        type: 'string'
 *      - name: 'accountId'
 *        in: 'query'
 *        description: '学生accountId'
 *        type: 'string'
 *      - name: 'teacher_id'
 *        in: 'query'
 *        description: '老师ID'
 *        type: 'string'
 *      - name: 'schedule_id'
 *        in: 'query'
 *        description: 'Schedule ID'
 *        type: 'string'
 *      - name: 'Course'
 *        in: 'query'
 *        description: '课程'
 *        type: 'string'
 *      - name: 'institutionUserId'
 *        in: 'query'
 *        description: 'institutionUserId'
 *        type: 'string'
 *      - name: 'endDate'
 *        in: 'query'
 *        description: '结束日期yyyy-mm-dd'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/te_class_new', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const startDate = req.query.startDate != undefined ? req.query.startDate : '2020-09-01';
    const ClassscheduleStatus = req.query.ClassscheduleStatus != undefined ? req.query.ClassscheduleStatus : '';
    const ClassscheduleZone = req.query.ClassscheduleZone != undefined ? req.query.ClassscheduleZone : '';
    const Course = req.query.Course != undefined ? req.query.Course : '';
    const institutionUserId = req.query.institutionUserId != undefined ? req.query.institutionUserId : '';
    const leaveState = req.query.leaveState != undefined ? req.query.leaveState : '';
    const student_id = req.query.student_id != undefined ? req.query.student_id : '';
    const accountId = req.query.accountId != undefined ? req.query.accountId : '';
    const teacher_id = req.query.teacher_id != undefined ? req.query.teacher_id : '';
    const endDate = req.query.endDate != undefined ? req.query.endDate : '';
    const department_id = req.query.department_id != undefined ? req.query.department_id : '';
    const showOther = req.query.showOther != undefined ? req.query.showOther : '0';
    const schedule_id = req.query.schedule_id != undefined ? req.query.schedule_id : ''

    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    // get BEC accountId list by institution user ID
    var accountIdList = [];
    if (institutionUserId.length > 0) {

        // get other institution user IDs under log in user
        var deptlist = [];
        if (showOther == '1' && department_id.length > 0) {
            // get department IDs
            await db.query("SELECT * from t_department where id ='" + department_id + "'").then(function (rows) {
                if (rows.length > 0) {
                    var templist = rows[0].path.split('/');
                    for (var i = 0; i < templist.length; i++) {
                        if (templist[i] != '') {
                            deptlist.push(templist[i]);
                        }
                    }
                    //deptlist = rows[0].path.split('/');
                }

            }).catch(function (err) {
            });

            // get institution user IDs in above departments
            var inst_user_list = [];
            if (deptlist.length > 0) {
                await db.query("SELECT * from t_institution_user where department_id in(" + deptlist + ")").then(function (rows) {
                    //accountIdList = rows;
                    for (var i = 0; i < rows.length; i++) {
                        inst_user_list.push("'" + rows[i].id + "'");
                    }
                }).catch(function (err) {
                });
            }

            if (inst_user_list.length > 0) {
                await db.query("SELECT  distinct(t_customer.zoho_user_id ) FROM t_customer LEFT JOIN t_household ON t_customer.household_id = t_household.id LEFT JOIN t_institution_household ON t_household.id = t_institution_household.household_id LEFT JOIN t_institution_user ON t_institution_household.institution_user_id = t_institution_user.id left join sys_user_role on t_institution_user.sys_user_id = sys_user_role.user_id LEFT JOIN sys_role on sys_user_role.role_id = sys_role.role_id WHERE  t_customer.type = '1' and t_institution_household.institution_user_id in (" + inst_user_list + ")").then(function (rows) {
                    //accountIdList = rows;
                    for (var i = 0; i < rows.length; i++) {
                        accountIdList.push("'" + rows[i].zoho_user_id + "'");
                    }
                }).catch(function (err) {
                });
            }


        } else {
            await db.query("SELECT  distinct(t_customer.zoho_user_id ) FROM t_customer LEFT JOIN t_household ON t_customer.household_id = t_household.id LEFT JOIN t_institution_household ON t_household.id = t_institution_household.household_id LEFT JOIN t_institution_user ON t_institution_household.institution_user_id = t_institution_user.id left join sys_user_role on t_institution_user.sys_user_id = sys_user_role.user_id LEFT JOIN sys_role on sys_user_role.role_id = sys_role.role_id WHERE  t_customer.type = '1' and t_institution_household.institution_user_id = '" + institutionUserId + "'").then(function (rows) {
                //accountIdList = rows;
                for (var i = 0; i < rows.length; i++) {
                    accountIdList.push("'" + rows[i].zoho_user_id + "'");
                }
            }).catch(function (err) {
            });
        }


    }



    var sqlFields = " classschedule.id,   classschedule.student_id,   student.`code`, student.accountId,  student.chineseName,    student.englishName,    student.`status` AS studentstatus,  student.classscheduleStatus AS studentclassschedulestatus,  classschedule.course_id,    course.`name` AS Course,    classschedule.scheduledDate,    classschedule.startTime,    classschedule.endTime,  classschedule.time, classschedule.teacher_id,   Teacher.chineseName AS TeacherCN,   Teacher.englishName AS TeacherEN,   Teacher.email AS TeacherEmail,  classschedule.teachingWay AS teachingwayid, teachingway.`name` AS TeachingWay,  classschedule.classroom_id, classroom.`name` AS Classroom,  classschedule.`status` AS ClassscheduleStatus,  classschedule.leaveState,   classschedule.description,  classschedule.creator_id,   Scheduler.englishName as Scheduler, classschedule.createTime,   classschedule.delStatus,    classschedule.endDate,  classschedule.BUType,   classschedule.zone_id,  zone.name as ClassscheduleZone, attendancebook.attendanceStatus,    attendancebook.createTime,  attendancebook.creator_id as signinpersonid,    signinperson.englishName as signinperson,   attendancebook.approvalStatus,  attendancebook.reason ";

    var sqlTable = " FROM  classschedule LEFT JOIN course ON classschedule.course_id = course.id LEFT JOIN student ON classschedule.student_id = student.id  LEFT JOIN USER as Teacher ON classschedule.teacher_id = Teacher.id  LEFT JOIN teachingway ON classschedule.teachingWay = teachingway.id LEFT JOIN classroom ON classschedule.classroom_id = classroom.id    left join user as Scheduler on classschedule.creator_id = Scheduler.id  left join Zone on classschedule.zone_id = zone.id   left join attendancebook on classschedule.id = attendancebook.classSchedule_id  left join user as signinperson on attendancebook.creator_id = signinperson.id ";

    var sqlWhere1 = ClassscheduleStatus.length > 0 ? " and classschedule.`status` = '" + ClassscheduleStatus + "' " : " and 1 = 1 ";
    var sqlWhere2 = ClassscheduleZone.length > 0 ? " and zone.name = '" + ClassscheduleZone + "' " : " and 1 = 1 ";
    var sqlWhere3 = leaveState.length > 0 ? " and leaveState = '" + leaveState + "' " : " and 1 = 1 ";
    var sqlWhere4 = student_id.length > 0 ? " and student_id = '" + student_id + "' " : " and 1 = 1 ";
    var sqlWhere5 = teacher_id.length > 0 ? " and teacher_id = '" + teacher_id + "' " : " and 1 = 1 ";
    var sqlWhere6 = endDate.length > 0 ? " and endDate <= '" + endDate + "' " : " and 1 = 1 ";
    var sqlWhere7 = accountId.length > 0 ? " and accountId = '" + accountId + "' " : " and 1 = 1 ";
    var sqlWhere8 = Course.length > 0 ? " and course.name like '%" + Course + "%' " : " and 1 = 1 ";
    var sqlWhere9 = accountIdList.length > 0 ? " and student.accountId in (" + accountIdList + ")" : " and student.accountId in ('chris')";
    let sqlWhere10 = schedule_id.length > 0 ? " and classschedule.id = '" + schedule_id + "' " : " and 1 = 1 "
    var sqlOrderBy = " order by scheduledDate desc";

    if (institutionUserId.length > 0) {
        var sqlWhere = " WHERE classschedule.delStatus <> '1' and classschedule.`status` <> '2' and scheduledDate >= '" + startDate + "' " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4 + sqlWhere5 + sqlWhere6 + sqlWhere7 + sqlWhere8 + sqlWhere9 + sqlWhere10;
    } else {
        var sqlWhere = " WHERE classschedule.delStatus <> '1' and classschedule.`status` <> '2' and scheduledDate >= '" + startDate + "' " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4 + sqlWhere5 + sqlWhere6 + sqlWhere7 + sqlWhere8 + sqlWhere10;
    }


    var sqlPaging = " limit ?, ?"

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere + sqlOrderBy + sqlPaging, params).then(function (rows) {
        result.list = rows;

    }).catch(function (err) {
    })

    result.pageNum = pageNum;
    result.pageSize = pageSize;

    // // get the customer ID of BEC
    // for (let i = 0; i < result.list.length; i++) {
    //     let household_id = 0
    //     await db.query("SELECT id, household_id from t_customer where zoho_user_id ='" + result.list[i].accountId + "'").then(function (rows) {
    //         if (rows.length > 0) {
    //             result.list[i].customerId = rows[0].id
    //             household_id = rows[0].household_id
    //         } else {
    //             result.list[i].customerId = ''
    //         }
    //     }).catch(function (err) {
    //     });

    //     // get inst user id, name, email according to household ID
    //     await db.query("SELECT id, institution_user_id, household_id, customer_id, sys_user_id, email from view_customer_inst_user where household_id = " + household_id).then(function (rows) {
    //         if (rows.length > 0) {
    //             result.list[i].institutionUserId = rows[0].institution_user_id
    //             result.list[i].sys_user_id = rows[0].sys_user_id
    //             result.list[i].email = rows[0].email
    //         }
    //     }).catch(function (err) {
    //     });
    // }

    // var scheduleId_list = [];

    // // determine if last day of a week

    // var week = [];
    // for (var i = 0; i < result.list.length; i++) {
    //     var weekindex = -1;
    //     var localDate = convertUTCTimeToLocalTime(result.list[i].scheduledDate);
    //     //var weekNum = getWeek(new Date(localDate.substring(0, 10)));
    //     var weekNum = getSaturdayNumber(getLastDayOfWeek(new Date(localDate.substring(0, 10)))) < 10 ? '0' + getSaturdayNumber(getLastDayOfWeek(new Date(localDate.substring(0, 10)))) : getSaturdayNumber(getLastDayOfWeek(new Date(localDate.substring(0, 10))));
    //     result.list[i].weeks = getLastDayOfWeek(new Date(localDate.substring(0, 10))).getFullYear().toString() + weekNum;

    //     for (var j = 0; j < week.length; j++) {
    //         if (week[j].week == weekNum && week[j].student_id == result.list[i].student_id && week[j].teacher_id == result.list[i].teacher_id && week[j].course_id == result.list[i].course_id && week[j].year == result.list[i].weeks.substring(0, 4)) {
    //             weekindex = j;
    //         }
    //     }
    //     if (weekindex >= 0) {
    //         if (localDate > week[weekindex].scheduledDate) {
    //             week[weekindex].scheduledDate = localDate;
    //             week[weekindex].maxindex = i;
    //         }
    //     } else {
    //         // current weekNum doesn't exist, push into week list
    //         week.push({ 'week': weekNum, 'student_id': result.list[i].student_id, 'teacher_id': result.list[i].teacher_id, 'course_id': result.list[i].course_id, 'scheduledDate': result.list[i].scheduledDate, 'maxindex': i, 'year': result.list[i].weeks.substring(0, 4) });
    //     }


    //     scheduleId_list.push("'" + result.list[i].id + "'");
    // }

    // for (var i = 0; i < week.length; i++) {
    //     result.list[week[i].maxindex].isLast = 'y';
    // }

    // // get feedback status
    // var feedback_status_list = [];
    // if (scheduleId_list.length > 0) {

    //     await db.query("SELECT id,schedule_id, status from t_feedback where schedule_id in (" + scheduleId_list + ")").then(function (rows) {
    //         for (var i = 0; i < rows.length; i++) {
    //             feedback_status_list.push({ 'schedule_id': rows[i].schedule_id, 'status': rows[i].status, 'feedback_id': rows[i].id });
    //         }
    //     }).catch(function (err) {
    //     });
    // }

    // // get homework
    // var homework_list = [];
    // if (scheduleId_list.length > 0) {
    //     await db.query("SELECT id,course_id from t_homework where course_id in (" + scheduleId_list + ")").then(function (rows) {
    //         for (var i = 0; i < rows.length; i++) {
    //             homework_list.push({ 'course_id': rows[i].course_id, 'homework_id': rows[i].id });
    //         }
    //     }).catch(function (err) {
    //     });
    // }

    // for (var i = 0; i < result.list.length; i++) {
    //     for (var j = 0; j < feedback_status_list.length; j++) {
    //         if (result.list[i].id == feedback_status_list[j].schedule_id) {
    //             result.list[i].feedback_status = feedback_status_list[j].status;
    //             result.list[i].feedback_id = feedback_status_list[j].feedback_id
    //         } else {
    //             result.list[i].feedback_status = '';
    //         }
    //     }
    // }

    // for (var i = 0; i < result.list.length; i++) {
    //     for (var j = 0; j < homework_list.length; j++) {
    //         if (result.list[i].id == homework_list[j].course_id) {
    //             result.list[i].homework_id = homework_list[j].homework_id
    //         }
    //     }
    // }


    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;
    res.send(data);

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/te_class:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取TE系统排课数据。如果没有参数则默认：pageNum=1, pageSize=15, startDate=2020-09-01
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'department_id'
 *        in: 'query'
 *        description: '部门ID'
 *        type: 'integer'
 *      - name: 'showOther'
 *        in: 'query'
 *        description: '是否显示下属（0:否 1:是）'
 *        type: 'string'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'startDate'
 *        in: 'query'
 *        description: '开始日期yyyy-mm-dd'
 *        type: 'string'
 *      - name: 'ClassscheduleStatus'
 *        in: 'query'
 *        description: '状态'
 *        type: 'string'
 *      - name: 'ClassscheduleZone'
 *        in: 'query'
 *        description: '校区'
 *        type: 'string'
 *      - name: 'leaveState'
 *        in: 'query'
 *        description: '请假状态'
 *        type: 'string'
 *      - name: 'student_id'
 *        in: 'query'
 *        description: '学生ID'
 *        type: 'string'
 *      - name: 'student_name'
 *        in: 'query'
 *        description: '学生姓名（模糊查询）'
 *        type: 'string'
 *      - name: 'accountId'
 *        in: 'query'
 *        description: '学生accountId'
 *        type: 'string'
 *      - name: 'teacher_id'
 *        in: 'query'
 *        description: '老师ID'
 *        type: 'string'
 *      - name: 'schedule_id'
 *        in: 'query'
 *        description: 'Schedule ID'
 *        type: 'string'
 *      - name: 'Course'
 *        in: 'query'
 *        description: '课程'
 *        type: 'string'
 *      - name: 'institutionUserId'
 *        in: 'query'
 *        description: 'institutionUserId'
 *        type: 'string'
 *      - name: 'endDate'
 *        in: 'query'
 *        description: '结束日期yyyy-mm-dd'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/te_class', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const startDate = req.query.startDate != undefined ? req.query.startDate : '2020-09-01';
    const ClassscheduleStatus = req.query.ClassscheduleStatus != undefined ? req.query.ClassscheduleStatus : '';
    const ClassscheduleZone = req.query.ClassscheduleZone != undefined ? req.query.ClassscheduleZone : '';
    const Course = req.query.Course != undefined ? req.query.Course : '';
    const institutionUserId = req.query.institutionUserId != undefined ? req.query.institutionUserId : '';
    const leaveState = req.query.leaveState != undefined ? req.query.leaveState : '';
    const student_id = req.query.student_id != undefined ? req.query.student_id : '';
    const student_name = req.query.student_name != undefined ? req.query.student_name : '';
    const accountId = req.query.accountId != undefined ? req.query.accountId : '';
    const teacher_id = req.query.teacher_id != undefined ? req.query.teacher_id : '';
    const endDate = req.query.endDate != undefined ? req.query.endDate : '';
    const department_id = req.query.department_id != undefined ? req.query.department_id : '';
    const showOther = req.query.showOther != undefined ? req.query.showOther : '0';
    const schedule_id = req.query.schedule_id != undefined ? req.query.schedule_id : ''

    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    // get BEC accountId list by institution user ID
    var accountIdList = [];
    if (institutionUserId.length > 0) {

        // get other institution user IDs under log in user
        var deptlist = [];
        if (showOther == '1' && department_id.length > 0) {
            // get department IDs
            await db.query("SELECT * from t_department where id ='" + department_id + "'").then(function (rows) {
                if (rows.length > 0) {
                    var templist = rows[0].path.split('/');
                    for (var i = 0; i < templist.length; i++) {
                        if (templist[i] != '') {
                            deptlist.push(templist[i]);
                        }
                    }
                    //deptlist = rows[0].path.split('/');
                }

            }).catch(function (err) {
            });

            // get institution user IDs in above departments
            var inst_user_list = [];
            if (deptlist.length > 0) {
                await db.query("SELECT * from t_institution_user where department_id in(" + deptlist + ")").then(function (rows) {
                    //accountIdList = rows;
                    for (var i = 0; i < rows.length; i++) {
                        inst_user_list.push("'" + rows[i].id + "'");
                    }
                }).catch(function (err) {
                });
            }

            if (inst_user_list.length > 0) {
                await db.query("SELECT  distinct(t_customer.zoho_user_id ) FROM t_customer LEFT JOIN t_household ON t_customer.household_id = t_household.id LEFT JOIN t_institution_household ON t_household.id = t_institution_household.household_id LEFT JOIN t_institution_user ON t_institution_household.institution_user_id = t_institution_user.id left join sys_user_role on t_institution_user.sys_user_id = sys_user_role.user_id LEFT JOIN sys_role on sys_user_role.role_id = sys_role.role_id WHERE  t_customer.type = '1' and t_institution_household.institution_user_id in (" + inst_user_list + ")").then(function (rows) {
                    //accountIdList = rows;
                    for (var i = 0; i < rows.length; i++) {
                        accountIdList.push("'" + rows[i].zoho_user_id + "'");
                    }
                }).catch(function (err) {
                });
            }


        } else {
            await db.query("SELECT  distinct(t_customer.zoho_user_id ) FROM t_customer LEFT JOIN t_household ON t_customer.household_id = t_household.id LEFT JOIN t_institution_household ON t_household.id = t_institution_household.household_id LEFT JOIN t_institution_user ON t_institution_household.institution_user_id = t_institution_user.id left join sys_user_role on t_institution_user.sys_user_id = sys_user_role.user_id LEFT JOIN sys_role on sys_user_role.role_id = sys_role.role_id WHERE  t_customer.type = '1' and t_institution_household.institution_user_id = '" + institutionUserId + "'").then(function (rows) {
                //accountIdList = rows;
                for (var i = 0; i < rows.length; i++) {
                    accountIdList.push("'" + rows[i].zoho_user_id + "'");
                }
            }).catch(function (err) {
            });
        }


    }



    var sqlFields = " classschedule.id,   classschedule.student_id,   student.`code`, student.accountId,  student.chineseName,    student.englishName,    student.`status` AS studentstatus,  student.classscheduleStatus AS studentclassschedulestatus,  classschedule.course_id,    course.`name` AS Course,    classschedule.scheduledDate,    classschedule.startTime,    classschedule.endTime,  classschedule.time, classschedule.teacher_id,   Teacher.chineseName AS TeacherCN,   Teacher.englishName AS TeacherEN,   Teacher.email AS TeacherEmail,  classschedule.teachingWay AS teachingwayid, teachingway.`name` AS TeachingWay,  classschedule.classroom_id, classroom.`name` AS Classroom,  classschedule.`status` AS ClassscheduleStatus,  classschedule.leaveState,   classschedule.description,  classschedule.creator_id,   Scheduler.englishName as Scheduler, classschedule.createTime,   classschedule.delStatus,    classschedule.endDate,  classschedule.BUType,   classschedule.zone_id,  zone.name as ClassscheduleZone, attendancebook.attendanceStatus,    attendancebook.createTime,  attendancebook.creator_id as signinpersonid,    signinperson.englishName as signinperson,   attendancebook.approvalStatus,  attendancebook.reason ";

    var sqlTable = " FROM  classschedule LEFT JOIN course ON classschedule.course_id = course.id LEFT JOIN student ON classschedule.student_id = student.id  LEFT JOIN USER as Teacher ON classschedule.teacher_id = Teacher.id  LEFT JOIN teachingway ON classschedule.teachingWay = teachingway.id LEFT JOIN classroom ON classschedule.classroom_id = classroom.id    left join user as Scheduler on classschedule.creator_id = Scheduler.id  left join Zone on classschedule.zone_id = zone.id   left join attendancebook on classschedule.id = attendancebook.classSchedule_id  left join user as signinperson on attendancebook.creator_id = signinperson.id ";

    var sqlWhere1 = ClassscheduleStatus.length > 0 ? " and classschedule.`status` = '" + ClassscheduleStatus + "' " : " and 1 = 1 ";
    var sqlWhere2 = ClassscheduleZone.length > 0 ? " and zone.name = '" + ClassscheduleZone + "' " : " and 1 = 1 ";
    var sqlWhere3 = leaveState.length > 0 ? " and leaveState = '" + leaveState + "' " : " and 1 = 1 ";
    var sqlWhere4 = student_id.length > 0 ? " and student_id = '" + student_id + "' " : " and 1 = 1 ";
    var sqlWhere5 = teacher_id.length > 0 ? " and teacher_id = '" + teacher_id + "' " : " and 1 = 1 ";
    var sqlWhere6 = endDate.length > 0 ? " and endDate <= '" + endDate + "' " : " and 1 = 1 ";
    var sqlWhere7 = accountId.length > 0 ? " and accountId = '" + accountId + "' " : " and 1 = 1 ";
    var sqlWhere8 = Course.length > 0 ? " and course.name like '%" + Course + "%' " : " and 1 = 1 ";
    var sqlWhere9 = accountIdList.length > 0 ? " and student.accountId in (" + accountIdList + ")" : " and student.accountId in ('chris')";
    let sqlWhere10 = schedule_id.length > 0 ? " and classschedule.id = '" + schedule_id + "' " : " and 1 = 1 "
    let sqlWhere11 = student_name.length > 0 ? " and student.englishName like '%" + student_name + "%' " : " and 1 = 1 "
    var sqlOrderBy = " order by scheduledDate desc";

    if (institutionUserId.length > 0) {
        var sqlWhere = " WHERE classschedule.delStatus <> '1' and classschedule.`status` <> '2' and scheduledDate >= '" + startDate + "' " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4 + sqlWhere5 + sqlWhere6 + sqlWhere7 + sqlWhere8 + sqlWhere9 + sqlWhere10 + sqlWhere11;
    } else {
        var sqlWhere = " WHERE classschedule.delStatus <> '1' and classschedule.`status` <> '2' and scheduledDate >= '" + startDate + "' " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4 + sqlWhere5 + sqlWhere6 + sqlWhere7 + sqlWhere8 + sqlWhere10 + sqlWhere11;
    }


    var sqlPaging = " limit ?, ?"

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere + sqlOrderBy + sqlPaging, params).then(function (rows) {
        result.list = rows;

    }).catch(function (err) {
    })

    result.pageNum = pageNum;
    result.pageSize = pageSize;

    // get the customer ID of BEC
    for (let i = 0; i < result.list.length; i++) {
        let household_id = 0
        await db.query("SELECT id, household_id from t_customer where zoho_user_id ='" + result.list[i].accountId + "'").then(function (rows) {
            if (rows.length > 0) {
                result.list[i].customerId = rows[0].id
                household_id = rows[0].household_id
            } else {
                result.list[i].customerId = ''
            }
        }).catch(function (err) {
        });
        // get family info list
        const options = {
            method: 'POST',
            headers: { 'content-type': 'application/json' },
            data: JSON.stringify({
                'customerId': result.list[i].customerId,
            }),
            url: 'https://connect.be.co:8200/customerUser/queryFamilyInfo'
        };
        //console.log(options);
        let result2 = await axios(options);
        result.list[i].advisers = result2.data.result.advisers
    }

    var scheduleId_list = [];

    // determine if last day of a week
    var week = [];
    for (var i = 0; i < result.list.length; i++) {
        var weekindex = -1;
        var localDate = convertUTCTimeToLocalTime(result.list[i].scheduledDate);
        //var weekNum = getWeek(new Date(localDate.substring(0, 10)));
        var weekNum = getSaturdayNumber(getLastDayOfWeek(new Date(localDate.substring(0, 10)))) < 10 ? '0' + getSaturdayNumber(getLastDayOfWeek(new Date(localDate.substring(0, 10)))) : getSaturdayNumber(getLastDayOfWeek(new Date(localDate.substring(0, 10))));
        result.list[i].weeks = getLastDayOfWeek(new Date(localDate.substring(0, 10))).getFullYear().toString() + weekNum;

        for (var j = 0; j < week.length; j++) {
            if (week[j].week == weekNum && week[j].student_id == result.list[i].student_id && week[j].teacher_id == result.list[i].teacher_id && week[j].course_id == result.list[i].course_id && week[j].year == result.list[i].weeks.substring(0, 4)) {
                weekindex = j;
            }
        }
        if (weekindex >= 0) {
            if (localDate > week[weekindex].scheduledDate) {
                week[weekindex].scheduledDate = localDate;
                week[weekindex].maxindex = i;
            }
        } else {
            // current weekNum doesn't exist, push into week list
            week.push({ 'week': weekNum, 'student_id': result.list[i].student_id, 'teacher_id': result.list[i].teacher_id, 'course_id': result.list[i].course_id, 'scheduledDate': result.list[i].scheduledDate, 'maxindex': i, 'year': result.list[i].weeks.substring(0, 4) });
        }
        scheduleId_list.push("'" + result.list[i].id + "'");

        // determine if first class schedule of certain student and course
        // await db_te.query("SELECT * FROM classschedule WHERE createTime  = (SELECT MIN(createTime) FROM classschedule where student_id = '" + result.list[i].student_id + "' and course_id = '" + result.list[i].course_id + "')").then(function (rows) {
        //     cs = rows[0];
        //     if (cs.id == result.list[i].id) {
        //         result.list[i].isFirst = 'y';
        //     }
        // }).catch(function (err) {
        // })
    }

    for (var i = 0; i < week.length; i++) {
        result.list[week[i].maxindex].isLast = 'y';
    }

    // get feedback status
    var feedback_status_list = [];
    if (scheduleId_list.length > 0) {

        await db.query("SELECT id,schedule_id, status from t_feedback where schedule_id in (" + scheduleId_list + ")").then(function (rows) {
            for (var i = 0; i < rows.length; i++) {
                feedback_status_list.push({ 'schedule_id': rows[i].schedule_id, 'status': rows[i].status, 'feedback_id': rows[i].id });
            }
        }).catch(function (err) {
        });
    }

    // get homework
    var homework_list = [];
    if (scheduleId_list.length > 0) {
        await db.query("SELECT id, schedule_id from t_homework where schedule_id in (" + scheduleId_list + ")").then(function (rows) {
            for (var i = 0; i < rows.length; i++) {
                homework_list.push({ 'schedule_id': rows[i].schedule_id, 'homework_id': rows[i].id });
            }
        }).catch(function (err) {
        });
    }

    for (var i = 0; i < result.list.length; i++) {
        for (var j = 0; j < feedback_status_list.length; j++) {
            if (result.list[i].id == feedback_status_list[j].schedule_id) {
                result.list[i].feedback_status = feedback_status_list[j].status;
                result.list[i].feedback_id = feedback_status_list[j].feedback_id
                break
            } else {
                result.list[i].feedback_status = '';
            }
        }
    }
    
    // get study plan
    var studyplan_list = [];
    if (scheduleId_list.length > 0) {
        await db.query("SELECT id, study_plan_id, classschedule_id from t_study_plan_weekly where classschedule_id in (" + scheduleId_list + ")").then(function (rows) {
            for (var i = 0; i < rows.length; i++) {
                studyplan_list.push({ 'studyplanweekly_id': rows[i].id, 'studyplan_id': rows[i].study_plan_id, 'classschedule_id': rows[i].classschedule_id });
            }
        }).catch(function (err) {
        });
    }

    for (var i = 0; i < result.list.length; i++) {
        for (var j = 0; j < homework_list.length; j++) {
            if (result.list[i].id == homework_list[j].schedule_id) {
                result.list[i].homework_id = homework_list[j].homework_id
            }
        }

        for (let k = 0; k < studyplan_list.length; k++) {
            if (result.list[i].id == studyplan_list[k].classschedule_id) {
                result.list[i].studyplanweekly_id = studyplan_list[k].studyplanweekly_id
                result.list[i].studyplan_id = studyplan_list[k].studyplan_id
            }
        }
    }

    
    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;
    res.send(data);

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/cases:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取Case数据。如果没有参数则默认：pageNum=1, pageSize=15；传入showOther=1，加上departmentId，则可以查询所有下属数据；传入showOther!=1，加上operator，则查询登录用户数据
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'showOther'
 *        in: 'query'
 *        description: '是否显示下属（0:否 1:是）'
 *        type: 'string'
 *      - name: 'showCaseNumber'
 *        in: 'query'
 *        description: '是否显示下属Case数量（0:否 1:是） 注：需要showOther=1，并且传入departmentId'
 *        type: 'string'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'departmentId'
 *        in: 'query'
 *        description: '部门ID'
 *        type: 'integer'
 *      - name: 'operator'
 *        in: 'query'
 *        description: 'Sys User ID'
 *        type: 'integer'
 *      - name: 'type'
 *        in: 'query'
 *        description: '类型Type'
 *        type: 'string'
 *      - name: 'productLine'
 *        in: 'query'
 *        description: 'productLine'
 *        type: 'string'
 *      - name: 'name'
 *        in: 'query'
 *        description: '名称'
 *        type: 'string'
 *      - name: 'statusList'
 *        in: 'query'
 *        description: '状态列表[1, 2, 3]'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/cases', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const departmentId = req.query.departmentId != undefined ? req.query.departmentId : 0;
    const operator = req.query.operator != undefined ? req.query.operator : 0;
    const type = req.query.type != undefined ? req.query.type : '';
    const productLine = req.query.productLine != undefined ? req.query.productLine : '';
    const name = req.query.name != undefined ? req.query.name : '';
    const statusList = req.query.statusList != undefined ? req.query.statusList : [];
    const showOther = req.query.showOther != undefined ? req.query.showOther : '0';
    const showCaseNumber = req.query.showCaseNumber != undefined ? req.query.showCaseNumber : '0';

    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    // get BEC accountId list by institution user ID
    var accountIdList = [];

    // get other institution user IDs under log in user
    var deptlist = [];
    if (showOther == '1' && departmentId.length > 0) {
        // get department IDs
        await db.query("SELECT * from t_department where id ='" + departmentId + "'").then(function (rows) {
            if (rows.length > 0) {
                var templist = rows[0].path.split('/');
                for (var i = 0; i < templist.length; i++) {
                    if (templist[i] != '') {
                        deptlist.push(templist[i]);
                    }
                }
                //deptlist = rows[0].path.split('/');
            }

        }).catch(function (err) {
        });
    }

    // get case num
    if (showCaseNumber == '1') {

        // get user list based on department list
        await db.query("SELECT * from t_institution_user where department_id in (" + deptlist.join(',') + ")").then(function (rows) {
            for (var i = 0; i < rows.length; i++) {
                accountIdList.push({ operator: rows[i].sys_user_id, showName: rows[i].show_name });
            }
        }).catch(function (err) {
        });
        if (accountIdList.length > 0) {
            var caseNumList = [];
            for (var i = 0; i < accountIdList.length; i++) {
                await db.query("select  (select count(t_project.id) from t_project inner join t_project_user_rel on t_project_user_rel.project_id = t_project.id where t_project_user_rel.sys_user_id = " + accountIdList[i].operator + " and t_project.product_line =1 and t_project.status <> 3 ) + ( select count(*) from t_project where product_line =1 and status <> 3 and operator = " + accountIdList[i].operator + " ) as total").then(function (rows) {
                    for (var j = 0; j < rows.length; j++) {
                        var caseNum = {};
                        caseNum.operator = accountIdList[i].operator;
                        caseNum.showName = accountIdList[i].showName;
                        caseNum.total = rows[j].total;
                        caseNumList.push(caseNum);
                    }
                }).catch(function (err) {
                });
            }

        }
        result.list = caseNumList;

        data.code = 0;
        data.message = 'SUCCESS';
        data.result = result;
        res.send(data);

    } else {
        var sqlFields = " DISTINCT ( `t_project`.`id`), `t_project`.`name`, `t_project`.`type`, `t_project`.`project_desc`, `t_project`.`customize_type` , `t_project`.`product_line`, `t_project`.`teaching_style`, `t_project`.`nation_id` , `t_project`.`contract_no` , `t_project`.`school_id` , `t_project`.`operator` , `t_project`.`seat_num` , `t_project`.`subject` , `t_project`.`service_package_id` , `t_project`.`owner`, `t_project`.`course` , `t_project`.`target_attachement` , `t_project`.`target` , `t_project`.`project_school_name` , `t_project`.`age_end` , `t_project`.`age_start` , `t_project`.`start_time` , `t_project`.`end_time` , `t_project`.`sign_up_start_time` , `t_project`.`sign_up_limit_time` , `t_project`.`gmt_create`, `t_project`.`gmt_modified` , `t_project`.`is_valid` , `t_project`.`status` , `t_project`.`version`  ";

        var sqlTable = " FROM t_project INNER JOIN t_project_user_rel ON t_project_user_rel.project_id = t_project.id ";

        var sqlWhere1 = type.length > 0 ? " and `t_project`.`type` = '" + type + "' " : " and 1 = 1 ";
        var sqlWhere2 = productLine.length > 0 ? " and `t_project`.`product_line` = '" + productLine + "' " : " and 1 = 1 ";
        var sqlWhere3 = name.length > 0 ? " and `t_project`.`name` like '%" + name + "%' " : " and 1 = 1 ";
        var sqlWhere4 = statusList.length > 0 ? " and `t_project`.`status` in (" + statusList + ")" : " and 1 = 1 ";

        if (showOther == '1') {
            var sqlWhere = " WHERE (t_project.`owner` IN ( SELECT sys_user_id FROM t_institution_user WHERE department_id IN (" + deptlist + " )) OR t_project_user_rel.sys_user_id IN ( SELECT sys_user_id FROM t_institution_user WHERE department_id IN (" + deptlist + " )))" + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4;
        } else {
            var sqlWhere = " WHERE (t_project.`owner` = " + operator + " or t_project_user_rel.sys_user_id = " + operator + ")" + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4;
        }

        sqlWhere += " order by `t_project`.`gmt_create` desc";

        var sqlPaging = " limit ?, ?"

        var projectList = [];
        //get total records number
        await db.query('select count(DISTINCT ( `t_project`.`id` )) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
            result.totalNum = rows[0].totalNum;
            result.totalPage = getTotalPageNum(result.totalNum, pageSize);
        }).catch(function (err) {
        })

        await db.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
            projectList = rows;

        }).catch(function (err) {
        })

        for (var i = 0; i < projectList.length; i++) {
            var sys_user_list = [];
            var customList = [];
            var userList = [];
            // get sys user id
            await db.query("select * from t_project_user_rel where project_id ='" + projectList[i].id + "'").then(function (rows) {
                sys_user_list = rows;

            }).catch(function (err) {
            });

            for (var j = 0; j < sys_user_list.length; j++) {
                // type is students
                if (sys_user_list[j].type == '1') {
                    await db.query("select * from t_customer where sys_user_id ='" + sys_user_list[j].sys_user_id + "'").then(function (rows) {
                        customList.push(rows[0]);
                    }).catch(function (err) {
                    })
                } else { // type is user
                    await db.query("select * from t_institution_user where sys_user_id ='" + sys_user_list[j].sys_user_id + "'").then(function (rows) {
                        userList.push(rows[0]);

                    }).catch(function (err) {
                    })
                }
            }

            // get owner (operator) detail
            var operatorDetail = {};
            await db.query("select * from t_institution_user where sys_user_id ='" + projectList[i].operator + "'").then(function (rows) {
                operatorDetail = rows[0];

            }).catch(function (err) {
            });

            projectList[i].customList = customList;
            projectList[i].userList = userList;
            projectList[i].operatorDetail = operatorDetail;
        }

        result.pageNum = pageNum;
        result.pageSize = pageSize;
        result.list = projectList;

        data.code = 0;
        data.message = 'SUCCESS';
        data.result = result;
        res.send(data);
    }



});



/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/te_class_by_institutionUserId:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取TE系统排课数据。如果没有参数则默认：pageNum=1, pageSize=15, startDate=2020-09-01
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'startDate'
 *        in: 'query'
 *        description: '开始日期yyyy-mm-dd'
 *        type: 'string'
 *      - name: 'institutionUserId'
 *        in: 'query'
 *        description: 'institutionUserId'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/te_class_by_institutionUserId', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const startDate = req.query.startDate != undefined ? req.query.startDate : '2020-09-01';
    const institutionUserId = req.query.institutionUserId != undefined ? req.query.institutionUserId : '';
    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var accountIdList = [];
    await db.query("SELECT  distinct(t_customer.zoho_user_id ) FROM t_customer LEFT JOIN t_household ON t_customer.household_id = t_household.id LEFT JOIN t_institution_household ON t_household.id = t_institution_household.household_id LEFT JOIN t_institution_user ON t_institution_household.institution_user_id = t_institution_user.id left join sys_user_role on t_institution_user.sys_user_id = sys_user_role.user_id LEFT JOIN sys_role on sys_user_role.role_id = sys_role.role_id WHERE  t_customer.type = '1' and t_institution_household.institution_user_id = '" + institutionUserId + "'").then(function (rows) {
        for (var i = 0; i < rows.length; i++) {
            accountIdList.push("'" + rows[i].zoho_user_id + "'");
        }
    }).catch(function (err) {
    });

    if (accountIdList.length > 0) {
        var sqlFields = " classschedule.id,   classschedule.student_id,   student.`code`, student.accountId,  student.chineseName,    student.englishName,    student.`status` AS studentstatus,  student.classscheduleStatus AS studentclassschedulestatus,  classschedule.course_id,    course.`name` AS Course,    classschedule.scheduledDate,    classschedule.startTime,    classschedule.endTime,  classschedule.time, classschedule.teacher_id,   Teacher.chineseName AS TeacherCN,   Teacher.englishName AS TeacherEN,   Teacher.email AS TeacherEmail,  classschedule.teachingWay AS teachingwayid, teachingway.`name` AS TeachingWay,  classschedule.classroom_id, classroom.`name` AS Classroom,  classschedule.`status` AS ClassscheduleStatus,  classschedule.leaveState,   classschedule.description,  classschedule.creator_id,   Scheduler.englishName as Scheduler, classschedule.createTime,   classschedule.delStatus,    classschedule.endDate,  classschedule.BUType,   classschedule.zone_id,  zone.name as ClassscheduleZone, attendancebook.attendanceStatus,    attendancebook.createTime,  attendancebook.creator_id as signinpersonid,    signinperson.englishName as signinperson,   attendancebook.approvalStatus,  attendancebook.reason ";

        var sqlTable = " FROM  classschedule LEFT JOIN course ON classschedule.course_id = course.id LEFT JOIN student ON classschedule.student_id = student.id  LEFT JOIN USER as Teacher ON classschedule.teacher_id = Teacher.id  LEFT JOIN teachingway ON classschedule.teachingWay = teachingway.id LEFT JOIN classroom ON classschedule.classroom_id = classroom.id    left join user as Scheduler on classschedule.creator_id = Scheduler.id  left join Zone on classschedule.zone_id = zone.id   left join attendancebook on classschedule.id = attendancebook.classSchedule_id  left join user as signinperson on attendancebook.creator_id = signinperson.id ";

        //var sqlWhere1 = institutionUserId.length > 0 ? " and classschedule.`status` = '" + institutionUserId + "' " : " and 1 = 1 ";
        var sqlWhere1 = " and student.accountId in (" + accountIdList + ")";

        var sqlWhere = " WHERE classschedule.delStatus <> '1' and scheduledDate >= '" + startDate + "' " + sqlWhere1;

        var sqlPaging = " limit ?, ?"

        //get total records number
        await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
            result.totalNum = rows[0].totalNum;
            result.totalPage = getTotalPageNum(result.totalNum, pageSize);
        }).catch(function (err) {
        })

        await db_te.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
            result.list = rows;
            result.pageNum = pageNum;
            result.pageSize = pageSize;

            data.code = 0;
            data.message = 'SUCCESS';
            data.result = result;

            res.send(data);
        }).catch(function (err) {
        })
    } else {
        data.code = 0;
        data.message = 'SUCCESS';
        data.result = 'no_data';
        res.send(data);
    }



});

/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/te_students:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取TE系统学生数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'englishName'
 *        in: 'query'
 *        description: '学生英文名'
 *        type: 'string'
 *      - name: 'chineseName'
 *        in: 'query'
 *        description: '学生中文名'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/te_students', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const englishName = req.query.englishName != undefined ? req.query.englishName : '';
    const chineseName = req.query.chineseName != undefined ? req.query.chineseName : '';
    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var sqlFields = " * ";

    var sqlTable = " from student ";

    var sqlWhere1 = englishName.length > 0 ? " and englishName like '%" + englishName + "%' " : " and 1 = 1 ";
    var sqlWhere2 = chineseName.length > 0 ? " and chineseName like '%" + chineseName + "%' " : " and 1 = 1 ";
    var sqlWhere = " where status <> '4' " + sqlWhere1 + sqlWhere2;

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        result.list = rows;
        result.pageNum = pageNum;
        result.pageSize = pageSize;

        data.code = 0;
        data.message = 'SUCCESS';
        data.result = result;

        res.send(data);
    }).catch(function (err) {
    })

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/te_teachers:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取TE系统老师数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'englishName'
 *        in: 'query'
 *        description: '老师英文名'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/te_teachers', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const englishName = req.query.englishName != undefined ? req.query.englishName : '';
    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var sqlFields = " * ";

    var sqlTable = " from user ";

    var sqlWhere1 = englishName.length > 0 ? " and englishName like '%" + englishName + "%' " : " and 1 = 1 ";
    var sqlWhere = " where isTeaching = '1' and status <> '2' " + sqlWhere1;

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        result.list = rows;
        result.pageNum = pageNum;
        result.pageSize = pageSize;

        data.code = 0;
        data.message = 'SUCCESS';
        data.result = result;

        res.send(data);
    }).catch(function (err) {
    })

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/te_contracts:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取TE系统合同数据。如果没有参数则默认：pageNum=1, pageSize=15, startDate=2020-09-01
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'startDate'
 *        in: 'query'
 *        description: '开始日期yyyy-mm-dd'
 *        type: 'string'
 *      
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/te_contracts', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const startDate = req.query.startDate != undefined ? req.query.startDate : '2020-09-01';
    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var sqlFields = " * ";

    var sqlTable = " from contract WHERE startDate >= '" + startDate + "' ";

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db_te.query('select' + sqlFields + sqlTable + sqlPaging, params).then(function (rows) {

        result.list = rows;
        result.pageNum = pageNum;
        result.pageSize = pageSize;

        data.code = 0;
        data.message = 'SUCCESS';
        data.result = result;

        res.send(data);
    }).catch(function (err) {
    })

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/student_consumption:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取TE系统中学生耗课数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'accountId'
 *        in: 'query'
 *        description: '学生accountId'
 *        type: 'string'
 *      
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/student_consumption', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const accountId = req.query.accountId != undefined ? req.query.accountId : '';
    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var sqlFields = " student.id, student.accountId, d.StudyAdvisorEmail, d.StudyAdvisor, d.StudyAdvisorID, student.ownerName AS Consultant, student.englishName AS Student, student.chineseName AS ChineseName, a.TotalHours, a.ConsumedClass, a.Balance, b.TotalSchedule, ( a.TotalHours - b.TotalSchedule ) AS ToBeScheduled, student.classscheduleStatus, ( CASE student.classscheduleStatus WHEN 0 THEN 'Active' WHEN 1 THEN 'Inactive' WHEN 3 THEN 'Closed' ELSE 'Half-Closed' END ) AS STATUS, CONCAT( cast((( a.ConsumedClass / a.TotalHours )* 100 ) AS DECIMAL ( 18, 2 )), '%' ) AS ConsumptionRate ";

    var sqlTable = " FROM student LEFT JOIN Zone ON zone.id = student.zone_id LEFT JOIN ( SELECT contract.studentId, sum( contract.amount * 2 ) AS TotalHours, sum( contract.consumedClass ) AS ConsumedClass,  (sum( contract.amount * 2 )- sum( contract.consumedClass ) - ifnull(BB.ConsumedClassNoContract, 0)) AS Balance FROM contract  left join (select student_id, sum(consumedClass) as ConsumedClassNoContract from view_consumption_student_contract where contract_id is null group by student_id) as BB on BB.student_id = contract.studentId where delStatus <> '1' GROUP BY contract.studentId ) AS a ON a.studentId = student.id LEFT JOIN ( SELECT classschedule.student_id AS studentId, sum( classschedule.time ) AS TotalSchedule FROM `classschedule` WHERE classschedule.`status` <> '2' GROUP BY classschedule.student_id ) AS b ON b.studentId = student.id LEFT JOIN ( SELECT * FROM ( SELECT student.chineseName, student.ownerName, USER.Englishname AS StudyAdvisor, user.id as StudyAdvisorID, user.email as StudyAdvisorEmail, tutor.* FROM `tutor` LEFT JOIN student ON tutor.student_id = student.id LEFT JOIN USER ON tutor.tutor_id = `user`.id ORDER BY startDate DESC ) AS c GROUP BY student_id ORDER BY startDate DESC ) AS d ON student.id = d.student_id ";

    var sqlWhere1 = accountId.length > 0 ? " and student.accountId = '" + accountId + "' " : " and 1 = 1 ";
    var sqlWhere = " where 1 = 1" + sqlWhere1;

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {

        result.list = rows;
        result.pageNum = pageNum;
        result.pageSize = pageSize;

        data.code = 0;
        data.message = 'SUCCESS';
        data.result = result;

        res.send(data);
    }).catch(function (err) {
    })

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/case:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取Case数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'YearGroup'
 *        in: 'query'
 *        description: 'YearGroup 数组：[12, 15]'
 *        type: 'string'
 *      - name: 'EntryDate'
 *        in: 'query'
 *        description: 'EntryDate'
 *        type: 'string'
 *      - name: 'status'
 *        in: 'query'
 *        description: 'status 数组：[1, 2]'
 *        type: 'string'
 *      - name: 'case_status'
 *        in: 'query'
 *        description: 'case_status 数组：[1, 2]'
 *        type: 'string'
 *      - name: 'showOther'
 *        in: 'query'
 *        description: '是否显示下属（0:否 1:是）'
 *        type: 'string'
 *      - name: 'departmentId'
 *        in: 'query'
 *        description: '部门ID'
 *        type: 'integer'
 *      - name: 'service_package_id'
 *        in: 'query'
 *        description: 'service_package_id 数组：[1, 2]'
 *        type: 'string'
 *      - name: 'consultantshowName'
 *        in: 'query'
 *        description: 'Consultant Show Name 模糊查询'
 *        type: 'string'
 *      - name: 'projectName'
 *        in: 'query'
 *        description: 'Project Name 模糊查询'
 *        type: 'string'
 *      - name: 'institution_user_id'
 *        in: 'query'
 *        description: 'institution_user_id'
 *        type: 'string'
 *      - name: 'schoolName'
 *        in: 'query'
 *        description: 'schoolName 模糊查询'
 *        type: 'string'

 *      
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/case', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;

    const YearGroup = req.query.YearGroup != undefined ? req.query.YearGroup : '';
    const EntryDate = req.query.EntryDate != undefined ? req.query.EntryDate : '';
    const status = req.query.status != undefined ? req.query.status : '';
    const service_package_id = req.query.service_package_id != undefined ? req.query.service_package_id : '';
    const consultantshowName = req.query.consultantshowName != undefined ? req.query.consultantshowName : '';
    const institution_user_id = req.query.institution_user_id != undefined ? req.query.institution_user_id : '';
    const schoolName = req.query.schoolName != undefined ? req.query.schoolName : '';
    const projectName = req.query.projectName != undefined ? req.query.projectName : '';
    const case_status = req.query.case_status != undefined ? req.query.case_status : '';
    const departmentId = req.query.departmentId != undefined ? req.query.departmentId : 0;
    const showOther = req.query.showOther != undefined ? req.query.showOther : '0';

    const pages = 0;

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    // get other institution user IDs under log in user
    var deptlist = [];
    var userList = [];
    var SACList = [];
    if (showOther == '1' && departmentId.length > 0) {
        // get department IDs
        await db.query("SELECT * from t_department where id ='" + departmentId + "'").then(function (rows) {
            if (rows.length > 0) {
                var templist = rows[0].path.split('/');
                for (var i = 0; i < templist.length; i++) {
                    if (templist[i] != '') {
                        deptlist.push(templist[i]);
                    }
                }
                //deptlist = rows[0].path.split('/');
            }

        }).catch(function (err) {
        });

        // get institution user list
        await db.query("select id from t_institution_user where department_id in (" + deptlist + ")").then(function (rows) {
            if (rows.length > 0) {
                for (var i = 0; i < rows.length; i++) {
                    if (rows[i].id != '') {
                        userList.push(rows[i].id);
                    }
                }
            }
            //userList = rows;
        }).catch(function (err) {
        })

    }

    var sqlFields = " distinct(t_project.id) AS CaseId, t_project.NAME, t_project.type, t_project.case_status, t_project.product_line, t_project.contract_no, t_project.service_package_id, t_project.`owner` AS CaseOwnerSysId, t_institution_user.show_name AS CaseOwner, t_project.`status`, t_project.start_time, t_project.end_time, st.tasks, t_institution_user.id as CaseInsId, t_project.gmt_create ";

    var sqlTable = " FROM t_project LEFT JOIN sys_user ON t_project.`owner` = sys_user.user_id LEFT JOIN t_institution_user ON t_institution_user.sys_user_id = sys_user.user_id LEFT JOIN t_project_user_rel ON t_project.id = t_project_user_rel.project_id  AND t_project_user_rel.type = '1' LEFT JOIN t_customer ON t_customer.sys_user_id = t_project_user_rel.sys_user_id LEFT JOIN ( SELECT DISTINCT t_customer.id AS CustomerId, count( t_task.id ) AS Tasks  FROM `t_task_user_rel` LEFT JOIN t_task ON t_task.id = t_task_user_rel.task_id LEFT JOIN t_customer ON t_task_user_rel.sys_user_id = t_customer.sys_user_id  WHERE t_task_user_rel.type = '3'  AND t_task.`status` = '1'  GROUP BY t_customer.id  ) AS st ON t_customer.id = st.CustomerId LEFT JOIN ( SELECT t_project_school.project_id as CaseId, t_customer.id AS CustomerId, t_customer.zoho_user_id AS AccountId, t_customer.show_name,  t_project.`name`, t_project.`status`, t_project_school.biz_school_id as SchoolId, t_biz_school.en_name as SchoolEnName, t_biz_school.zh_name as SchoolCnName, t_project_school.student_id, t_project_school.enrollment_year as YearGroup, t_project_school.entry_date as EntryDate, t_biz_school_apply.apply_result as ApplyResult, t_biz_school_apply.offer_type as OfferType, t_biz_school_apply.offer_status as OfferStatus, t_biz_school_apply.apply_status as ApplyStatus, t_biz_school_apply.has_scholarship as HasScholarship   FROM `t_project_school`  left join t_biz_school on t_project_school.biz_school_id = t_biz_school.id LEFT JOIN t_customer on t_project_school.student_id = t_customer.id left join t_project on t_project_school.project_id = t_project.id left join t_biz_school_apply on t_biz_school_apply.project_id = t_project.id and t_biz_school_apply.biz_school_id = t_project_school.biz_school_id  WHERE t_project_school.biz_school_id IS NOT NULL  ) AS CTS ON CTS.CaseId = t_project.id  left join ( SELECT t_project.id AS CaseId, t_project_user_rel.sys_user_id, t_project_user_rel.type AS ProjectUserType, t_project_user_rel.`status` AS ProjectUserStatus, consultant.sys_user_id AS ConsultantSysId, consultant.id as ConsultantInstId, consultant.show_name AS CaseConsultant, consultant.job_title as JobTitle FROM t_project LEFT JOIN t_project_user_rel ON t_project_user_rel.project_id = t_project.id  AND t_project_user_rel.type <> '1' LEFT JOIN t_institution_user consultant ON consultant.sys_user_id = t_project_user_rel.sys_user_id   ) as CC on CC.CaseId = t_project.id ";

    var sqlWhere1 = YearGroup.length > 0 ? " and CTS.YearGroup in (" + YearGroup + ") " : " and 1 = 1 ";
    var sqlWhere2 = EntryDate.length > 0 ? " and CTS.EntryDate = '" + EntryDate + "' " : " and 1 = 1 ";
    var sqlWhere3 = status.length > 0 ? " and t_project.`status` in (" + status + ") " : " and 1 = 1 ";
    if (showOther == '1') {
        var sqlWhere4 = userList.length > 0 ? " and ( ConsultantInstId in (" + userList + ") or t_institution_user.id in (" + userList + ") )" : " and ConsultantInstId in ('chris') ";
    } else {
        var sqlWhere4 = institution_user_id.length > 0 ? " and ( ConsultantInstId = '" + institution_user_id + "' or t_institution_user.id = '" + institution_user_id + "' )" : " and 1 = 1 ";
    }
    var sqlWhere5 = service_package_id.length > 0 ? " and t_project.service_package_id in (" + service_package_id + ") " : " and 1 = 1 ";
    var sqlWhere6 = consultantshowName.length > 0 ? " and CC.CaseConsultant like '%" + consultantshowName + "%' " : " and 1 = 1 ";
    var sqlWhere7 = schoolName.length > 0 ? " and CTS.SchoolEnName like '%" + schoolName + "%' " : " and 1 = 1 ";
    var sqlWhere8 = projectName.length > 0 ? " and t_project.NAME like '%" + projectName + "%' " : " and 1 = 1 ";
    var sqlWhere9 = case_status.length > 0 ? " and t_project.case_status in (" + case_status + ") " : " and 1 = 1 ";
    var sqlWhere = " where t_project.is_valid = '1' " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4 + sqlWhere5 + sqlWhere6 + sqlWhere7 + sqlWhere8 + sqlWhere9;

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db.query('select count(distinct(t_project.id)) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    // student list
    var case_list = [];
    await db.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        case_list = rows;
    }).catch(function (err) {
    })

    for (var i = 0; i < case_list.length; i++) {


        // get student target school list
        await db.query("SELECT t_customer.id AS CustomerId, t_customer.zoho_user_id AS AccountId, t_customer.sys_user_id, t_customer.show_name, t_project_school.project_id, t_project.`name`, t_project.`status`, t_project_school.biz_school_id, t_biz_school.en_name, t_biz_school.zh_name, t_project_school.student_id, t_project_school.enrollment_year, t_project_school.entry_date, t_biz_school_apply.apply_result, t_biz_school_apply.offer_type, t_biz_school_apply.offer_status, t_biz_school_apply.apply_status, t_project_school.gmt_create   FROM `t_project_school`  inner	join t_biz_school on t_project_school.biz_school_id = t_biz_school.id  LEFT JOIN t_customer on t_project_school.student_id = t_customer.sys_user_id left join t_project on t_project_school.project_id = t_project.id  left join t_biz_school_apply on t_biz_school_apply.project_school_id = t_project_school.id WHERE t_project_school.biz_school_id IS NOT NULL and t_project_school.is_valid = '1' and t_project_school.project_id ='" + case_list[i].CaseId + "' order by CustomerId,t_project_school.gmt_create").then(function (rows) {
            case_list[i].targetschool_list = rows;
        }).catch(function (err) {
        });

        // get student consultant list
        // await db.query("SELECT t_project.id AS CaseId, t_project_user_rel.sys_user_id, t_project_user_rel.type AS ProjectUserType, t_project_user_rel.`status` AS ProjectUserStatus, consultant.sys_user_id AS consultantSysId, consultant.show_name AS CaseConsultant, consultant.job_title  FROM t_project LEFT JOIN t_project_user_rel ON t_project_user_rel.project_id = t_project.id  AND t_project_user_rel.type <> '1' LEFT JOIN t_institution_user consultant ON consultant.sys_user_id = t_project_user_rel.sys_user_id  where t_project.id ='" + case_list[i].CaseId + "' ORDER BY job_title").then(function (rows) {
        //     case_list[i].student_consultant_list = rows;
        // }).catch(function (err) {
        // });

        var sys_user_list = [];
        var customList = [];
        var userList = [];
        // get sys user id
        await db.query("select * from t_project_user_rel where project_id ='" + case_list[i].CaseId + "'").then(function (rows) {
            sys_user_list = rows;

        }).catch(function (err) {
        });

        for (var j = 0; j < sys_user_list.length; j++) {
            // type is students
            if (sys_user_list[j].type == '1') {
                await db.query("select * from t_customer where sys_user_id ='" + sys_user_list[j].sys_user_id + "'").then(function (rows) {
                    customList.push(rows[0]);
                }).catch(function (err) {
                })
            } else { // type is user
                await db.query("select * from t_institution_user where sys_user_id ='" + sys_user_list[j].sys_user_id + "'").then(function (rows) {
                    userList.push(rows[0]);

                }).catch(function (err) {
                })
            }
        }

        // get owner (operator) detail
        var operatorDetail = {};
        await db.query("select * from t_institution_user where sys_user_id ='" + case_list[i].CaseOwnerSysId + "'").then(function (rows) {
            operatorDetail = rows[0];

        }).catch(function (err) {
        });

        case_list[i].customList = customList;
        case_list[i].userList = userList;
        case_list[i].operatorDetail = operatorDetail;

        // get case and task list

        await db.query("SELECT t_project.id AS CaseId, t_project.NAME, t_project.type, t_project.product_line, t_project.contract_no, t_project.service_package_id, t_project.`owner` AS CaseOwnerSysId, t_project.`status`, t_project.start_time, t_project.end_time, t_project_user_rel.sys_user_id AS StudentSysId, t_customer.show_name, t_institution_user.show_name AS CaseOwner  FROM t_project LEFT JOIN sys_user ON t_project.`owner` = sys_user.user_id LEFT JOIN t_institution_user ON t_institution_user.sys_user_id = sys_user.user_id LEFT JOIN t_project_user_rel ON t_project.id = t_project_user_rel.project_id  AND t_project_user_rel.type = '1' LEFT JOIN t_customer ON t_customer.sys_user_id = t_project_user_rel.sys_user_id left JOIN( SELECT DISTINCT t_customer.id as CustomerId, count(t_task.id) as Tasks FROM `t_task_user_rel` left join t_task on t_task.id = t_task_user_rel.task_id left join t_customer on t_task_user_rel.sys_user_id = t_customer.sys_user_id where t_task_user_rel.type = '3' and t_task.`status` = '1' group by t_customer.id ) as st on t_customer.id = st.CustomerId  where t_project.id ='" + case_list[i].CaseId + "'").then(function (rows) {
            case_list[i].case_task = rows;
        }).catch(function (err) {
        });


    }

    result.list = case_list;
    result.pageNum = pageNum;
    result.pageSize = pageSize;

    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;

    res.send(data);

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/student_view:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取BE Connect中学生顾问、目标院校、SA合同以及耗课数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'major_adviser'
 *        in: 'query'
 *        description: 'major_adviser'
 *        type: 'string'
 *      - name: 'SACStatus'
 *        in: 'query'
 *        description: 'SACStatus (9：显示无SA合同的列表)'
 *        type: 'string'
 *      - name: 'isObtStudent'
 *        in: 'query'
 *        description: '是否OBT学生 (0：否 1：是)'
 *        type: 'string'
 *      - name: 'SACproduct_name'
 *        in: 'query'
 *        description: 'SACproduct_name'
 *        type: 'string'
 *      - name: 'showOther'
 *        in: 'query'
 *        description: '是否显示下属（0:否 1:是）'
 *        type: 'string'
 *      - name: 'departmentId'
 *        in: 'query'
 *        description: '部门ID'
 *        type: 'integer'
 *      - name: 'showName'
 *        in: 'query'
 *        description: 'Student Show Name'
 *        type: 'string'
 *      - name: 'consultantshowName'
 *        in: 'query'
 *        description: 'Consultant Show Name'
 *        type: 'string'
 *      - name: 'institution_user_id'
 *        in: 'query'
 *        description: 'institution_user_id'
 *        type: 'string'
 *      - name: 'enrollment_year'
 *        in: 'query'
 *        description: 'enrollment_year'
 *        type: 'string'
 *      - name: 'entry_date'
 *        in: 'query'
 *        description: 'entry_date'
 *        type: 'string'
 *      - name: 'startDate'
 *        in: 'query'
 *        description: 'classschedule startDate'
 *        type: 'string'
 *      - name: 'endDate'
 *        in: 'query'
 *        description: 'classschedule endDate'
 *        type: 'string'
 *      - name: 'personalId'
 *        in: 'query'
 *        description: 'personalId'
 *        type: 'string'
 *      - name: 'SSStatus'
 *        in: 'query'
 *        description: 'SSStatus'
 *        type: 'string'
 *      - name: 'service_cat'
 *        in: 'query'
 *        description: 'Service Category'
 *        type: 'string'
 *      - name: 'te_status'
 *        in: 'query'
 *        description: 'TE student status (0: Active, 1: Inactive, 3: Closed other: Half-Closed)'
 *        type: 'string'
 *      
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/student_view', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const major_adviser = req.query.major_adviser != undefined ? req.query.major_adviser : '';
    const SACStatus = req.query.SACStatus != undefined ? req.query.SACStatus : '';
    const isObtStudent = req.query.isObtStudent != undefined ? req.query.isObtStudent : '';
    const SACproduct_name = req.query.SACproduct_name != undefined ? req.query.SACproduct_name : '';
    const institution_user_id = req.query.institution_user_id != undefined ? req.query.institution_user_id : '';
    const enrollment_year = req.query.enrollment_year != undefined ? req.query.enrollment_year : '';
    const entry_date = req.query.entry_date != undefined ? req.query.entry_date : '';
    const SSStatus = req.query.SSStatus != undefined ? req.query.SSStatus : '';
    const showName = req.query.showName != undefined ? req.query.showName : '';
    const consultantshowName = req.query.consultantshowName != undefined ? req.query.consultantshowName : '';
    const departmentId = req.query.departmentId != undefined ? req.query.departmentId : 0;
    const showOther = req.query.showOther != undefined ? req.query.showOther : '0';
    const service_cat = req.query.service_cat != undefined ? req.query.service_cat : '';
    const personalId = req.query.personalId != undefined ? req.query.personalId : '';
    const startDate = req.query.startDate != undefined ? req.query.startDate : '';
    const endDate = req.query.endDate != undefined ? req.query.endDate : '';
    const pages = 0;

    const te_status = req.query.te_status != undefined ? req.query.te_status : '';

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    // get other institution user IDs under log in user
    var deptlist = [];
    var userList = [];
    var SACList = [];
    var te_student_accountlist = [];
    var te_student_list = [];
    var te_schedule_accountList = [];
    var te_schedule_list = [];

    if (showOther == '1' && departmentId.length > 0) {
        // get department IDs
        await db.query("SELECT * from t_department where id ='" + departmentId + "'").then(function (rows) {
            if (rows.length > 0) {
                var templist = rows[0].path.split('/');
                for (var i = 0; i < templist.length; i++) {
                    if (templist[i] != '') {
                        deptlist.push(templist[i]);
                    }
                }
                //deptlist = rows[0].path.split('/');
            }

        }).catch(function (err) {
        });

        // get institution user list
        await db.query("select id from t_institution_user where department_id in (" + deptlist + ")").then(function (rows) {
            if (rows.length > 0) {
                for (var i = 0; i < rows.length; i++) {
                    if (rows[i].id != '') {
                        userList.push(rows[i].id);
                    }
                }
            }
            //userList = rows;
        }).catch(function (err) {
        })

    }

    if (personalId.length > 0 && startDate.length > 0 && endDate.length > 0) {
        var sqlFields = " classschedule.id,   classschedule.student_id,   student.`code`, student.accountId,  student.chineseName,    student.englishName,    student.`status` AS studentstatus,  student.classscheduleStatus AS studentclassschedulestatus,  classschedule.course_id,    course.`name` AS Course,    classschedule.scheduledDate,    classschedule.startTime,    classschedule.endTime,  classschedule.time, classschedule.teacher_id,   Teacher.chineseName AS TeacherCN,   Teacher.englishName AS TeacherEN,   Teacher.email AS TeacherEmail,  classschedule.teachingWay AS teachingwayid, teachingway.`name` AS TeachingWay,  classschedule.classroom_id, classroom.`name` AS Classroom,  classschedule.`status` AS ClassscheduleStatus,  classschedule.leaveState,   classschedule.description,  classschedule.creator_id,   Scheduler.englishName as Scheduler, classschedule.createTime,   classschedule.delStatus,    classschedule.endDate,  classschedule.BUType,   classschedule.zone_id,  zone.name as ClassscheduleZone, attendancebook.attendanceStatus,    attendancebook.createTime,  attendancebook.creator_id as signinpersonid,    signinperson.englishName as signinperson,   attendancebook.approvalStatus,  attendancebook.reason ";

        var sqlTable = " FROM  classschedule LEFT JOIN course ON classschedule.course_id = course.id LEFT JOIN student ON classschedule.student_id = student.id  LEFT JOIN USER as Teacher ON classschedule.teacher_id = Teacher.id  LEFT JOIN teachingway ON classschedule.teachingWay = teachingway.id LEFT JOIN classroom ON classschedule.classroom_id = classroom.id    left join user as Scheduler on classschedule.creator_id = Scheduler.id  left join Zone on classschedule.zone_id = zone.id   left join attendancebook on classschedule.id = attendancebook.classSchedule_id  left join user as signinperson on attendancebook.creator_id = signinperson.id ";

        var sqlWhere = " where classschedule.scheduledDate>='" + startDate + "' and classschedule.scheduledDate<='" + endDate + "' and classschedule.teacher_id='" + personalId + "'";

        await db_te.query('select' + sqlFields + sqlTable + sqlWhere).then(function (rows) {
            te_schedule_accountList
            if (rows.length > 0) {
                te_schedule_list = rows;
                for (var i = 0; i < rows.length; i++) {
                    if (rows[i].id != '') {
                        te_schedule_accountList.push("'" + rows[i].accountId + "'");
                    }
                }
            }

        }).catch(function (err) {
        })

    }

    //if (te_status.length > 0) {
    var tesqlFields = " student.id, student.accountId, d.StudyAdvisor, d.StudyAdvisorID, student.ownerName AS Consultant, student.englishName AS Student, student.chineseName AS ChineseName, a.TotalHours, a.ConsumedClass, a.Balance, b.TotalSchedule, ( a.TotalHours - b.TotalSchedule ) AS ToBeScheduled, student.classscheduleStatus, ( CASE student.classscheduleStatus WHEN 0 THEN 'Active' WHEN 1 THEN 'Inactive' WHEN 3 THEN 'Closed' ELSE 'Half-Closed' END ) AS STATUS, CONCAT( cast((( a.ConsumedClass / a.TotalHours )* 100 ) AS DECIMAL ( 18, 2 )), '%' ) AS ConsumptionRate ";

    var tesqlTable = " FROM student LEFT JOIN Zone ON zone.id = student.zone_id LEFT JOIN ( SELECT contract.studentId, sum( contract.amount * 2 ) AS TotalHours, sum( contract.consumedClass ) AS ConsumedClass,  (sum( contract.amount * 2 )- sum( contract.consumedClass ) - ifnull(BB.ConsumedClassNoContract, 0)) AS Balance FROM contract  left join (select student_id, sum(consumedClass) as ConsumedClassNoContract from view_consumption_student_contract where contract_id is null group by student_id) as BB on BB.student_id = contract.studentId where delStatus <> '1' GROUP BY contract.studentId ) AS a ON a.studentId = student.id LEFT JOIN ( SELECT classschedule.student_id AS studentId, sum( classschedule.time ) AS TotalSchedule FROM `classschedule` WHERE classschedule.`status` <> '2' GROUP BY classschedule.student_id ) AS b ON b.studentId = student.id LEFT JOIN ( SELECT * FROM ( SELECT student.chineseName, student.ownerName, USER.Englishname AS StudyAdvisor, user.id as StudyAdvisorID, tutor.* FROM `tutor` LEFT JOIN student ON tutor.student_id = student.id LEFT JOIN USER ON tutor.tutor_id = `user`.id ORDER BY startDate DESC ) AS c GROUP BY student_id ORDER BY startDate DESC ) AS d ON student.id = d.student_id ";

    var tesqlWhere1 = te_status.length > 0 ? " and student.classscheduleStatus = '" + te_status + "' " : " and 1 = 1 ";
    var tesqlWhere = " where 1 = 1" + tesqlWhere1;

    await db_te.query('select' + tesqlFields + tesqlTable + tesqlWhere).then(function (rows) {
        if (rows.length > 0) {
            te_student_list = rows;
            for (var i = 0; i < rows.length; i++) {
                if (rows[i].id != '') {
                    te_student_accountlist.push("'" + rows[i].accountId + "'");
                }
            }
        }
    }).catch(function (err) {
    })
    //}



    var sqlFields = " distinct(t_customer.id) as CustomerId, t_customer.show_name, t_customer.butype, 	t_customer.zoho_user_id as AccountId, t_customer.sys_user_id ";

    var sqlTable = " FROM `t_customer` LEFT JOIN t_institution_household ON t_customer.household_id = t_institution_household.household_id LEFT JOIN ( SELECT t_customer.id AS CustomerId, t_customer.zoho_user_id AS AccountId, t_customer.show_name, t_institution_household.institution_user_id, t_institution_user.sys_user_id, sys_user_role.role_id, sys_role.role_name, t_institution_household.major_adviser, t_institution_user.username, t_institution_user.show_name as con_showname, t_customer.butype as butype, t_household.id AS householdId, t_household.NAME FROM t_customer LEFT JOIN t_household ON t_customer.household_id = t_household.id LEFT JOIN t_institution_household ON t_household.id = t_institution_household.household_id LEFT JOIN t_institution_user ON t_institution_household.institution_user_id = t_institution_user.id LEFT JOIN sys_user_role ON t_institution_user.sys_user_id = sys_user_role.user_id LEFT JOIN sys_role ON sys_user_role.role_id = sys_role.role_id WHERE t_customer.type = '1' ) AS SHC ON SHC.CustomerId = t_customer.id LEFT JOIN ( SELECT t_customer.id AS CustomerId, t_customer.zoho_user_id AS AccountId, t_customer.show_name, t_customer_contract.contract_no, t_customer_contract.service_cat AS ServiceCategory, t_customer_contract.service_type ,t_customer_contract.`contract _name` as ContractName, t_customer_contract.product_line, t_customer_contract.product_name, t_customer_contract.start_time, t_customer_contract.end_time, t_customer_contract.amount, t_customer_contract.`status`, t_customer_contract.is_valid FROM t_customer INNER JOIN t_customer_contract ON t_customer_contract.customer_id = t_customer.id WHERE t_customer.type = '1'  AND t_customer_contract.product_line = '1' ) AS SAC ON SAC.CustomerId = t_customer.id LEFT JOIN ( SELECT t_customer.id AS CustomerId, t_customer.zoho_user_id AS AccountId, t_customer.show_name, t_project_school.project_id, t_project.`name`, t_project.`status`, t_project_school.biz_school_id, t_biz_school.en_name, t_biz_school.zh_name, t_project_school.student_id, t_project_school.enrollment_year, t_project_school.entry_date, t_biz_school_apply.apply_result, t_biz_school_apply.offer_type, t_biz_school_apply.offer_status FROM `t_project_school` INNER JOIN t_biz_school ON t_project_school.biz_school_id = t_biz_school.id LEFT JOIN t_customer ON t_project_school.student_id = t_customer.id LEFT JOIN t_project ON t_project_school.project_id = t_project.id LEFT JOIN t_biz_school_apply ON t_biz_school_apply.project_id = t_project_school.project_id WHERE t_project_school.biz_school_id IS NOT NULL ORDER BY CustomerId ) AS SS ON SS.CustomerId = t_customer.id ";

    var sqlWhere1 = major_adviser.length > 0 ? " and t_institution_household.major_adviser = '" + major_adviser + "' " : " and 1 = 1 ";
    var sqlWhere2 = "";
    if (SACStatus.length > 0) {
        if (SACStatus == '9') {
            sqlWhere2 = " and t_customer.id not in (SELECT DISTINCT(t_customer.id) FROM t_customer_contract inner join t_customer on t_customer_contract.customer_id = t_customer.id where t_customer_contract.product_line = '1')";
        } else {
            sqlWhere2 = " and SAC.`status` = '" + SACStatus + "' ";
        }
    } else {
        sqlWhere2 = " and 1 = 1 ";
    }
    var sqlWhere3 = SACproduct_name.length > 0 ? " and SAC.product_name = '" + SACproduct_name + "' " : " and 1 = 1 ";
    if (showOther == '1') {
        var sqlWhere4 = userList.length > 0 ? " and SHC.institution_user_id in (" + userList + ") " : " and SHC.institution_user_id in ('chris') ";
    } else {
        var sqlWhere4 = institution_user_id.length > 0 ? " and SHC.institution_user_id = '" + institution_user_id + "' " : " and 1 = 1 ";
    }
    var sqlWhere5 = enrollment_year.length > 0 ? " and SS.enrollment_year = '" + enrollment_year + "' " : " and 1 = 1 ";
    var sqlWhere6 = entry_date.length > 0 ? " and SS.entry_date <= '" + entry_date + "' " : " and 1 = 1 ";
    var sqlWhere7 = SSStatus.length > 0 ? " and SS.`status` = '" + SSStatus + "' " : " and 1 = 1 ";
    var sqlWhere8 = showName.length > 0 ? " and t_customer.show_name like '%" + showName + "%' " : " and 1 = 1 ";
    var sqlWhere9 = consultantshowName.length > 0 ? " and SHC.con_showname like '%" + consultantshowName + "%' " : " and 1 = 1 ";
    var sqlWhere10 = service_cat.length > 0 ? " and ServiceCategory like '%" + service_cat + "%' " : " and 1 = 1 ";

    var sqlWhere11 = ' and 1 = 1 ';
    if (te_status.length > 0) {
        sqlWhere11 = te_student_accountlist.length > 0 ? " and t_customer.zoho_user_id in (" + te_student_accountlist + ") " : " and t_customer.zoho_user_id in ('chris')";
    }

    var sqlWhere12 = ' and 1 = 1 ';
    if (personalId.length > 0 && startDate.length > 0 && endDate.length > 0) {
        sqlWhere12 = te_schedule_accountList.length > 0 ? " and t_customer.zoho_user_id in (" + te_schedule_accountList + ") " : " and t_customer.zoho_user_id in ('chris')";
    }

    let sqlWhere13 = ' and 1 = 1 '
    if (isObtStudent.length > 0) {
        if (isObtStudent == '1') {
            sqlWhere13 = " and (t_customer.online_te_id is not null or t_customer.online_te_id <> '')"
        } else if (isObtStudent == '0') {
            sqlWhere13 = " and (t_customer.online_te_id is null or t_customer.online_te_id = '')"
        }
    }
    var sqlWhere = " WHERE t_customer.type = '1' and t_customer.is_valid = '1' " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4 + sqlWhere5 + sqlWhere6 + sqlWhere7 + sqlWhere8 + sqlWhere9 + sqlWhere10 + sqlWhere11 + sqlWhere12 + sqlWhere13;

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db.query('select count(distinct(t_customer.id)) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    // student list
    console.log('select' + sqlFields + sqlTable + sqlWhere + sqlPaging)
    var student_list = [];
    await db.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        student_list = rows;
    }).catch(function (err) {
    })

    // get TE data
    // if (student_list.length > 0) {
    //     var student_account_list = [];
    //     var te_list = [];
    //     for (var i = 0; i < student_list.length; i++) {
    //         if (student_list[i].AccountId != undefined && student_list[i].AccountId.length > 0) {
    //             student_account_list.push("'" + student_list[i].AccountId + "'");
    //         }
    //     }
    //     await db_te.query("select student.id, student.accountId, d.StudyAdvisor, d.StudyAdvisorID, student.ownerName AS Consultant, student.englishName AS Student, student.chineseName AS ChineseName, a.TotalHours, a.ConsumedClass, a.Balance, b.TotalSchedule, ( a.TotalHours - b.TotalSchedule ) AS ToBeScheduled, student.classscheduleStatus, ( CASE student.classscheduleStatus WHEN 0 THEN 'Active' WHEN 1 THEN 'Inactive' WHEN 3 THEN 'Closed' ELSE 'Half-Closed' END ) AS STATUS, CONCAT( cast((( a.ConsumedClass / a.TotalHours )* 100 ) AS DECIMAL ( 18, 2 )), '%' ) AS ConsumptionRate FROM student LEFT JOIN Zone ON zone.id = student.zone_id LEFT JOIN ( SELECT contract.studentId, sum( contract.amount * 2 ) AS TotalHours, sum( contract.consumedClass ) AS ConsumedClass,  (sum( contract.amount * 2 )- sum( contract.consumedClass ) - ifnull(BB.ConsumedClassNoContract, 0)) AS Balance FROM contract  left join (select student_id, sum(consumedClass) as ConsumedClassNoContract from view_consumption_student_contract where contract_id is null group by student_id) as BB on BB.student_id = contract.studentId where delStatus <> '1' GROUP BY contract.studentId ) AS a ON a.studentId = student.id LEFT JOIN ( SELECT classschedule.student_id AS studentId, sum( classschedule.time ) AS TotalSchedule FROM `classschedule` WHERE classschedule.`status` <> '2' GROUP BY classschedule.student_id ) AS b ON b.studentId = student.id LEFT JOIN ( SELECT * FROM ( SELECT student.chineseName, student.ownerName, USER.Englishname AS StudyAdvisor, user.id as StudyAdvisorID, tutor.* FROM `tutor` LEFT JOIN student ON tutor.student_id = student.id LEFT JOIN USER ON tutor.tutor_id = `user`.id ORDER BY startDate DESC ) AS c GROUP BY student_id ORDER BY startDate DESC ) AS d ON student.id = d.student_id where student.accountId in (" + student_account_list + ")").then(function (rows) {
    //         te_list = rows;
    //     }).catch(function (err) {
    //     });
    // }


    for (var i = 0; i < student_list.length; i++) {


        // get student consultant list
        await db.query("SELECT t_customer.id AS CustomerId, t_customer.zoho_user_id AS AccountId, t_customer.sys_user_id, st.Tasks, t_customer.show_name, t_institution_household.institution_user_id, t_institution_user.sys_user_id, sys_user_role.role_id, sys_role.role_name, t_institution_household.major_adviser, t_institution_user.show_name, t_institution_user.username, t_household.id AS householdId, t_household.NAME  FROM t_customer LEFT JOIN t_household ON t_customer.household_id = t_household.id LEFT JOIN t_institution_household ON t_household.id = t_institution_household.household_id LEFT JOIN t_institution_user ON t_institution_household.institution_user_id = t_institution_user.id left join sys_user_role on t_institution_user.sys_user_id = sys_user_role.user_id LEFT JOIN sys_role on sys_user_role.role_id = sys_role.role_id left JOIN( SELECT DISTINCT t_customer.id as CustomerId, count(t_task.id) as Tasks FROM `t_task_user_rel` left join t_task on t_task.id = t_task_user_rel.task_id left join t_customer on t_task_user_rel.sys_user_id = t_customer.sys_user_id where t_task_user_rel.type = '3' and t_task.`status` = '1' group by t_customer.id ) as st on t_customer.id = st.CustomerId WHERE  t_customer.type = '1' and t_customer.id='" + student_list[i].CustomerId + "' order by CustomerId").then(function (rows) {
            student_list[i].student_consultant_list = rows;
        }).catch(function (err) {
        });



        // get student target school list
        await db.query("SELECT t_customer.id AS CustomerId, t_customer.zoho_user_id AS AccountId, t_customer.sys_user_id, t_customer.show_name, t_project_school.project_id, t_project.`name`, t_project.`status`, t_project_school.biz_school_id, t_biz_school.en_name, t_biz_school.zh_name, t_project_school.student_id, t_project_school.enrollment_year, t_project_school.entry_date, t_biz_school_apply.apply_result, t_biz_school_apply.offer_type, t_biz_school_apply.offer_status, t_biz_school_apply.apply_status, t_project_school.gmt_create FROM `t_project_school` inner join t_biz_school on t_project_school.biz_school_id = t_biz_school.id LEFT JOIN t_customer on t_project_school.student_id = t_customer.id left join t_project on t_project_school.project_id = t_project.id left join t_biz_school_apply on t_biz_school_apply.project_school_id = t_project_school.id WHERE t_project_school.biz_school_id IS NOT NULL and t_customer.id='" + student_list[i].CustomerId + "' order by CustomerId, t_project_school.gmt_create").then(function (rows) {
            student_list[i].targetschool_list = rows;
        }).catch(function (err) {
        });

        // get student contract list
        if (SACStatus != '9') {
            await db.query("SELECT t_customer.id AS CustomerId, t_customer.zoho_user_id AS AccountId, t_customer.show_name, t_customer_contract.contract_no, t_customer_contract.`contract _name`, t_customer_contract.product_line, t_customer_contract.service_type, t_customer_contract.service_cat, t_customer_contract.product_name, t_customer_contract.start_time, t_customer_contract.end_time, t_customer_contract.amount, t_customer_contract.`status`, t_customer_contract.is_valid  FROM t_customer INNER JOIN t_customer_contract ON t_customer_contract.customer_id = t_customer.id  WHERE t_customer.type = '1'  AND t_customer_contract.product_line = '1' and t_customer.id ='" + student_list[i].CustomerId + "'").then(function (rows) {
                student_list[i].contract_list = rows;
            }).catch(function (err) {
            });
        }


        // get TE student consumption data
        // await db_te.query("select student.id, student.accountId, d.StudyAdvisor, d.StudyAdvisorID, student.ownerName AS Consultant, student.englishName AS Student, student.chineseName AS ChineseName, a.TotalHours, a.ConsumedClass, a.Balance, b.TotalSchedule, ( a.TotalHours - b.TotalSchedule ) AS ToBeScheduled, student.classscheduleStatus, ( CASE student.classscheduleStatus WHEN 0 THEN 'Active' WHEN 1 THEN 'Inactive' WHEN 3 THEN 'Closed' ELSE 'Half-Closed' END ) AS STATUS, CONCAT( cast((( a.ConsumedClass / a.TotalHours )* 100 ) AS DECIMAL ( 18, 2 )), '%' ) AS ConsumptionRate FROM student LEFT JOIN Zone ON zone.id = student.zone_id LEFT JOIN ( SELECT contract.studentId, sum( contract.amount * 2 ) AS TotalHours, sum( contract.consumedClass ) AS ConsumedClass, ( sum( contract.amount * 2 )- sum( contract.consumedClass )) AS Balance FROM `contract` GROUP BY contract.studentId ) AS a ON a.studentId = student.id LEFT JOIN ( SELECT classschedule.student_id AS studentId, sum( classschedule.time ) AS TotalSchedule FROM `classschedule` WHERE classschedule.`status` <> '2' GROUP BY classschedule.student_id ) AS b ON b.studentId = student.id LEFT JOIN ( SELECT * FROM ( SELECT student.chineseName, student.ownerName, USER.Englishname AS StudyAdvisor, user.id as StudyAdvisorID, tutor.* FROM `tutor` LEFT JOIN student ON tutor.student_id = student.id LEFT JOIN USER ON tutor.tutor_id = `user`.id ORDER BY startDate DESC ) AS c GROUP BY student_id ORDER BY startDate DESC ) AS d ON student.id = d.student_id where student.accountId ='" + student_list[i].AccountId + "'").then(function (rows) {
        //     student_list[i].consumption_list = rows;
        // }).catch(function (err) {
        // });

        // for (var j = 0; j < te_list.length; j++) {
        //     if (te_list[j].accountId == student_list[i].AccountId) {
        //         student_list[i].consumption_list = te_list[j];
        //     }
        // }

        for (var j = 0; j < te_student_list.length; j++) {
            if (te_student_list[j].accountId == student_list[i].AccountId) {
                student_list[i].consumption_list = te_student_list[j];
            }
        }

        for (var k = 0; k < te_schedule_list.length; k++) {
            if (te_schedule_list[k].accountId == student_list[i].AccountId) {
                student_list[i].schedule_list = te_schedule_list[k];
            }
        }
    }

    result.list = student_list;
    result.pageNum = pageNum;
    result.pageSize = pageSize;

    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;

    res.send(data);

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/student_consumption_old:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取学生耗课数据，共查询4次（Active, Inactive, Halfclosed, Closed)。耗时长。
 *      parameters:
 *      - name: 'student_id'
 *        in: 'query'
 *        description: '学生ID'
 *        type: 'string'
 *      
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/student_consumption_old', async function (req, res, next) {
    const student_id = req.query.student_id != undefined ? req.query.student_id : '';
    var totalNum = 0;

    var result = {};
    var result_sql1 = [];
    var result_sql2 = [];
    var result_sql3 = [];
    var result_sql4 = [];

    var data = {};

    // get Active student consumption
    var sqlFields = " CONCAT(X.NAME, ' ', IFNULL(x.isonline, ' ')) AS City, X.SA, X.Englishname AS Student, X.ownername AS Consultant, X.TotalHours, X.totalhours - x.used1 - ifnull(Y.used2, 0) AS Balance, X.totalhours - x.used1 - ifnull(Y.used2, 0) - ifnull(Z.scheduled, 0) AS ToBeScheduled, x.zone_id, X.id student_id, concat( TRUNCATE( (x.used1 + ifnull(Y.used2, 0)) / X.TotalHours * 100, 2 ), '%' ) AS ConsumptionRate, 'Active' AS STATUS ";

    var sqlTable = " FROM view_x_idandcity X LEFT JOIN view_y Y ON X.id = Y.student_id LEFT JOIN view_z Z ON X.id = Z.student_id LEFT JOIN view_classSchedule_30 v30 ON X.id = v30.student_id ";

    var sqlWhere = " WHERE v30.student_id IS NOT NULL AND x.id = '" + student_id + "' ";

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere).then(function (rows) {

        result_sql1 = rows;

    }).catch(function (err) {
    });

    // get Inactive student consumption
    var sqlFields = " CONCAT(X. NAME,' ',IFNULL(x.isonline,' ')) AS City, X.SA, X.Englishname AS Student, X.ownername AS Consultant, X.TotalHours, X.totalhours - x.used1 - ifnull(Y.used2, 0) AS Balance, X.totalhours - x.used1 - ifnull(Y.used2, 0) - ifnull(Z.scheduled, 0) AS ToBeScheduled, x.zone_id, X.id student_id, concat( TRUNCATE ( (x.used1 + ifnull(Y.used2, 0)) / X.TotalHours * 100, 2 ), '%' ) AS ConsumptionRate, 'Inactive' AS STATUS  ";

    var sqlTable = " FROM view_x_id X LEFT JOIN view_y Y ON X.id = Y.student_id LEFT JOIN view_z Z ON X.id = Z.student_id LEFT JOIN view_classSchedule_30 v30 ON x.id = v30.student_id ";

    var sqlWhere = " WHERE X.id != 'Id_student00000367' AND X.id NOT IN ( SELECT DISTINCT studentid FROM contract WHERE isabandoned = 1 ) AND ISNULL(v30.student_id) AND X.id NOT IN ( SELECT A.id FROM ( SELECT vsl.id, sum(ifnull(amount, 0)) * 2 - sum(ifnull(consumedclass, 0)) AS Balance1 FROM view_student_list vsl RIGHT JOIN contract ON studentid = vsl.id WHERE vsl.id IS NOT NULL GROUP BY vsl.id ) A WHERE A.balance1 = 0 AND A.id NOT IN ( SELECT DISTINCT student_id FROM consumption_student_contract LEFT JOIN consumption_student ON consumption_student_id = consumption_student.id WHERE consumption_student_contract.contract_id IS NULL ) )  AND x.id = '" + student_id + "' ";

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere).then(function (rows) {

        result_sql2 = rows;

    }).catch(function (err) {
    })

    // get Halfclosed student consumption
    var sqlFields = " CONCAT(X. NAME,' ',IFNULL(x.isonline,' ')) AS City, X.SA, X.Englishname AS Student, X.ownername AS Consultant, X.TotalHours, X.totalhours - x.used1 - ifnull(Y.used2, 0) AS Balance, X.totalhours - x.used1 - ifnull(Y.used2, 0) - ifnull(Z.scheduled, 0) AS ToBeScheduled, x.zone_id, X.id student_id, concat( TRUNCATE ( (x.used1 + ifnull(Y.used2, 0)) / X.TotalHours * 100, 2 ), '%' ) AS ConsumptionRate, 'Half-closed' AS STATUS  ";

    var sqlTable = " FROM view_x_id X LEFT JOIN view_y Y ON X.id = Y.student_id LEFT JOIN view_z Z ON X.id = Z.student_id LEFT JOIN view_classSchedule_30 v30 ON x.id = v30.student_id  ";

    var sqlWhere = " WHERE v30.student_id IS NULL AND X.id IN ( SELECT DISTINCT studentid FROM contract WHERE isabandoned = 1 ) AND X.id NOT IN ( SELECT A.id FROM ( SELECT vsl.id, sum(ifnull(amount, 0)) * 2 - sum(ifnull(consumedclass, 0)) AS Balance1 FROM view_student_list vsl RIGHT JOIN contract ON studentid = vsl.id WHERE vsl.id IS NOT NULL GROUP BY vsl.id ) A WHERE A.balance1 = 0 AND A.id NOT IN ( SELECT DISTINCT student_id FROM consumption_student_contract LEFT JOIN consumption_student ON consumption_student_id = consumption_student.id WHERE consumption_student_contract.contract_id IS NULL ) ) AND x.id = '" + student_id + "' ";

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere).then(function (rows) {

        result_sql3 = rows;

    }).catch(function (err) {
    })

    // get Closed student consumption
    var sqlFields = " CONCAT(X. NAME,' ',IFNULL(x.isonline,' ')) AS City, X.SA, X.Englishname AS Student, X.ownername AS Consultant, X.TotalHours, X.totalhours - x.used1 - ifnull(Y.used2, 0) AS Balance, X.totalhours - x.used1 - ifnull(Y.used2, 0) - ifnull(Z.scheduled, 0) AS ToBeScheduled, x.zone_id, X.id student_id, concat( TRUNCATE ( (x.used1 + ifnull(Y.used2, 0)) / X.TotalHours * 100, 2 ), '%' ) AS ConsumptionRate, 'Closed' AS STATUS  ";

    var sqlTable = " FROM ( view_x_id X LEFT JOIN view_y Y ON X.id = Y.student_id ) LEFT JOIN view_z Z ON X.id = Z.student_id LEFT JOIN view_classSchedule_30 v30 ON x.id = v30.student_id  ";

    var sqlWhere = " WHERE ISNULL(v30.student_id) AND X.id IN ( SELECT A.id FROM ( SELECT vsl.id, sum(ifnull(amount, 0)) * 2 - sum(ifnull(consumedclass, 0)) AS Balance1 FROM view_student_list vsl RIGHT JOIN contract ON studentid = vsl.id WHERE vsl.id IS NOT NULL GROUP BY vsl.id ) A WHERE A.balance1 = 0 AND A.id NOT IN ( SELECT DISTINCT student_id FROM consumption_student_contract LEFT JOIN consumption_student ON consumption_student_id = consumption_student.id WHERE consumption_student_contract.contract_id IS NULL ) ) AND x.id = '" + student_id + "' ";

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere).then(function (rows) {

        result_sql4 = rows;

    }).catch(function (err) {
    })

    result.list = result_sql1.concat(result_sql2).concat(result_sql3).concat(result_sql4);

    result.totalNum = result.list.length;
    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;
    res.send(data);

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/connect_consultant:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取BE Connect中顾问数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'institution_user_id'
 *        in: 'query'
 *        description: 'institution用户ID'
 *        type: 'integer'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/connect_consultant', async function (req, res, next) {

    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const institution_user_id = req.query.institution_user_id != undefined ? req.query.institution_user_id : 0;
    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var sqlFields = " sys_role.role_name, t_institution_user.* ";

    var sqlTable = " from t_institution_user left join sys_user on sys_user.user_id = t_institution_user.sys_user_id left join sys_user_role on sys_user_role.user_id = sys_user.user_id left join sys_role on sys_role.role_id = sys_user_role.role_id ";

    var sqlWhere = " WHERE t_institution_user.id IN ( SELECT DISTINCT(institution_user_id) FROM t_institution_household WHERE household_id in( SELECT t_institution_household.household_id FROM `t_institution_household` INNER JOIN t_customer ON t_customer.household_id = t_institution_household.household_id WHERE t_institution_household.institution_user_id = " + institution_user_id + " AND t_customer.type = 1 ORDER BY t_customer.id) ) ";

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        result.list = rows;
        result.pageNum = pageNum;
        result.pageSize = pageSize;

        data.code = 0;
        data.message = 'SUCCESS';
        data.result = result;

        res.send(data);
    }).catch(function (err) {
    })

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/te_request_form:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取BE Connect中顾问数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'customer_id'
 *        in: 'query'
 *        description: 'Customer ID 用户ID'
 *        type: 'integer'
 *      - name: 'month_date'
 *        in: 'query'
 *        description: 'month_date 年-月'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/te_request_form', async function (req, res, next) {

    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const customer_id = req.query.customer_id != undefined ? req.query.customer_id : 0;
    const month_date = req.query.month_date != undefined ? req.query.month_date : '';
    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)];

    var sqlWhere1 = customer_id.length > 0 ? " and customer_id = '" + customer_id + "' " : " and 1 = 1 ";
    var sqlWhere2 = month_date.length > 0 ? " and month_date = '" + month_date + "' " : " and 1 = 1 ";

    var result = {};

    var data = {};

    var sqlFields = " * ";

    var sqlTable = " from t_request_form ";

    var sqlWhere = " WHERE is_valid <> '0' " + sqlWhere1 + sqlWhere2;

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    var request_form_list = [];
    await db.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        request_form_list = rows;
    }).catch(function (err) {
    })

    for (var i = 0; i < request_form_list.length; i++) {
        // get request_form_subject
        await db.query("SELECT * from t_request_form_subject WHERE is_valid <> '0' and request_form_id =' " + request_form_list[i].id + "' order by id desc").then(function (rows) {
            request_form_list[i].request_form_subject_list = rows;
        }).catch(function (err) {
        });
        for (var j = 0; j < request_form_list[i].request_form_subject_list.length; j++) {
            // get request_form_school
            await db.query("SELECT * from t_request_form_school WHERE is_valid <> '0' and request_form_subject_id =' " + request_form_list[i].request_form_subject_list[j].id + "' order by id desc").then(function (rows) {
                request_form_list[i].request_form_subject_list[j].request_form_school_list = rows;
            }).catch(function (err) {
            });
        }
    }

    result.list = request_form_list;
    result.pageNum = pageNum;
    result.pageSize = pageSize;

    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;

    res.send(data);

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/study_plan:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取BE Connect中顾问数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'customer_id'
 *        in: 'query'
 *        description: 'Customer ID 用户ID'
 *        type: 'integer'
 *      - name: 'month_date'
 *        in: 'query'
 *        description: 'month_date 年-月'
 *        type: 'string'
 *      - name: 'course'
 *        in: 'query'
 *        description: 'course'
 *        type: 'string'
 *      - name: 'teacher'
 *        in: 'query'
 *        description: 'teacher'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/study_plan', async function (req, res, next) {

    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const customer_id = req.query.customer_id != undefined ? req.query.customer_id : 0;
    const month_date = req.query.month_date != undefined ? req.query.month_date : '';
    const course = req.query.course != undefined ? req.query.course : '';
    const teacher = req.query.teacher != undefined ? req.query.teacher : '';
    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)];

    var sqlWhere1 = customer_id.length > 0 ? " and customer_id = '" + customer_id + "' " : " and 1 = 1 ";
    var sqlWhere2 = month_date.length > 0 ? " and month_date = '" + month_date + "' " : " and 1 = 1 ";
    var sqlWhere3 = course.length > 0 ? " and course = '" + course + "' " : " and 1 = 1 ";
    var sqlWhere4 = teacher.length > 0 ? " and teacher = '" + teacher + "' " : " and 1 = 1 ";

    var result = {};

    var data = {};

    var sqlFields = " * ";

    var sqlTable = " from t_study_plan ";

    var sqlWhere = " WHERE is_valid <> '0' " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4;

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    var study_plan_list = [];
    await db.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        study_plan_list = rows;
    }).catch(function (err) {
    })

    for (var i = 0; i < study_plan_list.length; i++) {
        // get request_form_subject
        await db.query("SELECT * from t_study_plan_weekly WHERE is_valid <> '0' and study_plan_id =' " + study_plan_list[i].id + "' order by id desc").then(function (rows) {
            study_plan_list[i].study_plan_weekly = rows;
        }).catch(function (err) {
        });
    }

    result.list = study_plan_list;
    result.pageNum = pageNum;
    result.pageSize = pageSize;

    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;

    res.send(data);

});


router.get('/te_request', async function (req, res, next) {

    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const institution_user_id = req.query.institution_user_id != undefined ? req.query.institution_user_id : 0;
    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var sqlFields = " sys_role.role_name, t_institution_user.* ";

    var sqlTable = " from t_institution_user left join sys_user on sys_user.user_id = t_institution_user.sys_user_id left join sys_user_role on sys_user_role.user_id = sys_user.user_id left join sys_role on sys_role.role_id = sys_user_role.role_id ";

    var sqlWhere = " WHERE t_institution_user.id IN ( SELECT DISTINCT(institution_user_id) FROM t_institution_household WHERE household_id in( SELECT t_institution_household.household_id FROM `t_institution_household` INNER JOIN t_customer ON t_customer.household_id = t_institution_household.household_id WHERE t_institution_household.institution_user_id = " + institution_user_id + " AND t_customer.type = 1 ORDER BY t_customer.id) ) ";

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        result.list = rows;
        result.pageNum = pageNum;
        result.pageSize = pageSize;

        data.code = 0;
        data.message = 'SUCCESS';
        data.result = result;

        res.send(data);
    }).catch(function (err) {
    })

});


router.get('/', async function (req, res, next) {

    const page_num = 3 //当前的num
    const page_size = 15 //当前页的数量
    const params = [(parseInt(page_num) - 1) * parseInt(page_size), parseInt(page_size)]

    var sql = "select * from sys_role limit ?, ?";
    var sql2 = "select * from sys_user limit ?, ?";

    var list = [];
    await db.query(sql, params).then(function (rows) {
        //res.send(rows);
        list = rows;
    }).catch(function (err) {
    })
    await db.query(sql2, params).then(function (rows) {
        //res.send(rows);
        list = list.concat(rows);
    }).catch(function (err) {
    })

    res.send(list);

});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/homework:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 获取BE Connect中顾问数据。如果没有参数则默认：pageNum=1, pageSize=15
 *      parameters:
 *      - name: 'id'
 *        in: 'query'
 *        description: 'Homework ID '
 *        type: 'string'
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'course_id'
 *        in: 'query'
 *        description: 'Course ID'
 *        type: 'string'
 *      - name: 'schedule_id'
 *        in: 'query'
 *        description: 'Schedule ID ** Dont use this param, use course_id instead'
 *        type: 'string'
 *      - name: 'customer_id'
 *        in: 'query'
 *        description: 'Customer ID '
 *        type: 'string'
 *      - name: 'status'
 *        in: 'query'
 *        description: 'Status '
 *        type: 'string'
 *      - name: 'homeworkYearMonth'
 *        in: 'query'
 *        description: 'Home Year-Month'
 *        type: 'string'
 *      - name: 'tutor_personal_id'
 *        in: 'query'
 *        description: 'Tutor Personal ID '
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/homework', async function (req, res, next) {

    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;
    const course_id = req.query.course_id != undefined ? req.query.course_id : 0;
    const customer_id = req.query.customer_id != undefined ? req.query.customer_id : 0;
    const tutor_personal_id = req.query.tutor_personal_id != undefined ? req.query.tutor_personal_id : 0;
    const schedule_id = req.query.schedule_id != undefined ? req.query.schedule_id : 0;
    const id = req.query.id != undefined ? req.query.id : 0
    const status = req.query.status != undefined ? req.query.status : ''
    const homeworkYearMonth = req.query.homeworkYearMonth != undefined ? req.query.homeworkYearMonth : ''

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)];

    var sqlWhere1 = course_id.length > 0 ? " and course_id = '" + course_id + "' " : " and 1 = 1 ";
    let sqlWhere2 = customer_id.length > 0 ? " and customer_id ='" + customer_id + "' " : " and 1 = 1"
    let sqlWhere3 = tutor_personal_id.length > 0 ? " and tutor_personal_id ='" + tutor_personal_id + "' " : " and 1 = 1"
    let sqlWhere4 = schedule_id.length > 0 ? " and schedule_id = '" + schedule_id + "' " : " and 1 = 1"
    let sqlWhere5 = id.length > 0 ? " and id = '" + id + "' " : " and 1 = 1"
    let sqlWhere6 = status.length > 0 ? " and status = '" + status + "' " : " and 1 = 1"
    let sqlWhere7 = " "
    if (homeworkYearMonth.length > 0) {
        let { firstDayOfMonth, lastDayOfMonth } = getFirstAndLastDayOfMonth(homeworkYearMonth)
        sqlWhere7 = ` and gmt_create >= '${firstDayOfMonth}' and gmt_create <= '${lastDayOfMonth} 23:59:59'`
    }

    var result = {};

    var data = {};

    var sqlFields = " * ";

    var sqlTable = " from t_homework ";

    var sqlWhere = " WHERE is_valid <> '0' " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4 + sqlWhere5 + sqlWhere6 + sqlWhere7

    var sqlPaging = " limit ?, ?"

    //get total records number
    await db.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    var homework_list = [];
    await db.query('select' + sqlFields + sqlTable + sqlWhere + sqlPaging, params).then(function (rows) {
        homework_list = rows;
    }).catch(function (err) {
    })

    result.list = homework_list;
    result.pageNum = pageNum;
    result.pageSize = pageSize;

    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;

    res.send(data);

});

/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/course_list:
 *  get:
 *      tags:
 *      - Get data from BEC & TE DB
 *      description: 根据学生和老师ID获取科目列表。
 *      parameters:
 *      - name: 'student_account_id'
 *        in: 'query'
 *        description: '学生AccountID'
 *        type: 'string'
 *      - name: 'teacher_id'
 *        in: 'query'
 *        description: '学生ID'
 *        type: 'string'
 *      
 *      
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/course_list', async function (req, res, next) {
    const student_account_id = req.query.student_account_id != undefined ? req.query.student_account_id : '';
    const teacher_id = req.query.teacher_id != undefined ? req.query.teacher_id : '';

    var result = {};
    var data = {};
    var sqlPaging = " limit ?, ?"

    // get Active student consumption
    var sqlFields = " t1.course_id, c.name ";

    var sqlTable = " from ( select distinct(course_id) from student_course inner join student on student.id = student_course.student_id  ";

    let sqlWhere = ''

    if (student_account_id.length > 0 && teacher_id.length > 0) {
        sqlWhere = " where student.accountId = '" + student_account_id + "' ) t1 inner join ( select distinct(course_id) from teacher_course where teacher_id = '" + teacher_id + "' ) as t2 on t2.course_id = t1.course_id inner join course c on c.id = t1.course_id ";
    } else if (student_account_id.length > 0 && teacher_id.length == 0) {
        sqlWhere = " where student.accountId = '" + student_account_id + "' ) t1  inner join course c on c.id = t1.course_id ";
    } else (
        res.send('student_account_id is required')
    )

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })

    await db_te.query('select' + sqlFields + sqlTable + sqlWhere).then(function (rows) {
        result.list = rows;

    }).catch(function (err) {
    })

    result.totalNum = result.list.length;
    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;
    res.send(data);

});

/**
 * @swagger
 * /beconnect_api/is_first:
 *   post:
 *     tags:
 *       - Is first class schedule
 *     description:  判断是否第一个classschedule
 *     produces:
 *       - application/json
 *     parameters:
 *       - name: req_body
 *         description: Request object
 *         in: body
 *         required: true
 *     responses:
 *       200:
 *         description: success
 */
router.post('/is_first', async function (req, res, next) {
    let body = ""
    let jsonObj = {}
    let csList = []
    req.on('data', function (chunk) {
        body += chunk
    });
    req.on('end', async function () {
        try {
            body = JSON.parse(body);
            jsonObj = body;
        } catch (err) {
            res.send(err.message);
        }
        // dedup
        const uniqueData = jsonObj.filter((obj, index, arr) => {
            return !arr.slice(0, index).some(prevObj => prevObj.student_id === obj.student_id && prevObj.course_id === obj.course_id);
        });
        csList = uniqueData

        for (let i = 0; i < csList.length; i++) {
            await db_te.query("SELECT id FROM classschedule WHERE createTime  = (SELECT MIN(createTime) FROM classschedule where student_id = '" + csList[i].student_id + "' and course_id = '" + csList[i].course_id + "')").then(function (rows) {
                let cs = rows[0];
                for (let j = 0; j < jsonObj.length; j++) {
                    if (cs.id == jsonObj[j].classschedule_id) {
                        jsonObj[j].isFirst = 'y'
                        break
                    }
                }
            }).catch(function (err) { })
        }
        let data = {}
        data.code = 0;
        data.message = 'SUCCESS';
        data.result = jsonObj;
        res.send(data);
    })
});


/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/tencent_class:
 *  get:
 *      tags:
 *      - Get data from TE DB
 *      description: 获取TE系统排课数据。
 *      parameters:
 *      - name: 'userid'
 *        in: 'query'
 *        description: '用户账号'
 *        type: 'string'
 *      - name: 'role'
 *        in: 'query'
 *        description: '类型'
 *        type: 'string'
 *      - name: 'startdate'
 *        in: 'query'
 *        description: 'Start Date'
 *        type: 'string'
 *      - name: 'enddate'
 *        in: 'query'
 *        description: 'End Date'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/tencent_class', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 100;

    const startDate = req.query.startdate != undefined ? req.query.startdate : '2020-09-01';
    const endDate = req.query.enddate != undefined ? req.query.enddate : '';
    const userid = req.query.userid != undefined ? req.query.userid : '';
    const role = req.query.role != undefined ? req.query.role : '';
    //const displayname = req.query.displayname != undefined ? req.query.displayname : '';
    //const url = req.query.url != undefined ? req.query.url : '';

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var sqlFields = " id, student_tencentid, teacher_tencentid, stu_chineseName, stu_englishName, us_englishName, scheduledDate, endDate, delStatus, `status`, tencenturl, startTime, endTime, accountId, teacheremail, course_name";

    var sqlTable = " FROM view_paike_class ";

    let sqlWhere1 = ' and 1 = 1 '
    if (role == 's') {
        sqlWhere1 = userid.length > 0 ? " and SUBSTRING(view_paike_class.accountId, -8) = '" + userid + "' " : " and 1 = 1 ";
    }
    if (role == 't') {
        sqlWhere1 = userid.length > 0 ? " and view_paike_class.teacheremail = '" + userid + "' " : " and 1 = 1 ";
    }
    let sqlWhere2 = endDate.length > 0 ? " and endDate <= '" + endDate + "' " : " and 1 = 1 ";
    var sqlWhere = " WHERE (tencenturl <> '' and tencenturl is not null) and view_paike_class.delStatus <> '1' and view_paike_class.`status` = '1' and scheduledDate >= '" + startDate + "' " + sqlWhere1 + sqlWhere2 
    var sqlOrderBy = " order by scheduledDate, startTime desc";
    var sqlPaging = " limit ?, ?"

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })
    console.log('select' + sqlFields + sqlTable + sqlWhere + sqlOrderBy + sqlPaging, params)
    await db_te.query('select' + sqlFields + sqlTable + sqlWhere + sqlOrderBy + sqlPaging, params).then(function (rows) {
        result.list = rows;

    }).catch(function (err) {
    })

    result.pageNum = pageNum;
    result.pageSize = pageSize;

    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;
    res.send(data);

});

/**
 * @swagger
 * 
 * 
 * 
 * /beconnect_api/tencent_users:
 *  get:
 *      tags:
 *      - Get data from TE DB
 *      description: 获取TE系统排课数据。
 *      parameters:
 *      - name: 'pageNum'
 *        in: 'query'
 *        description: '页码'
 *        type: 'integer'
 *      - name: 'pageSize'
 *        in: 'query'
 *        description: '每页行数'
 *        type: 'integer'
 *      - name: 'userid'
 *        in: 'query'
 *        description: '用户账号'
 *        type: 'string'
 *      - name: 'type'
 *        in: 'query'
 *        description: '类型'
 *        type: 'string'
 *      - name: 'name'
 *        in: 'query'
 *        description: 'Name'
 *        type: 'string'
 *      - name: 'del'
 *        in: 'query'
 *        description: 'Delete status'
 *        type: 'string'
 *           
 *      responses:
 *          '200':
 *              description: 返回列表
 * 
 */
router.get('/tencent_users', async function (req, res, next) {
    const pageNum = req.query.pageNum != undefined ? req.query.pageNum : 1;
    const pageSize = req.query.pageSize != undefined ? req.query.pageSize : 15;

    const del = req.query.del != undefined ? req.query.del : '';
    const name = req.query.name != undefined ? req.query.name : '';
    const userid = req.query.userid != undefined ? req.query.userid : '';
    const type = req.query.type != undefined ? req.query.type : '';

    const params = [(parseInt(pageNum) - 1) * parseInt(pageSize), parseInt(pageSize)]

    var result = {};

    var data = {};

    var sqlFields = " id, zohoid, userid, type, name, created, del, lanmaoid, accountid";

    var sqlTable = " FROM view_tencentaccount ";

    let sqlWhere1 = " and 1 = 1 ";
    if (del == 'active') {
        sqlWhere1 = " and del <> '1' ";
    } else if (del == 'deleted') {
        sqlWhere1 = " and del = '1' ";
    }
    let sqlWhere2 = name.length > 0 ? " and name like '%" + name + "%' " : " and 1 = 1 ";
    let sqlWhere3 = userid.length > 0 ? " and userid = '" + userid + "' " : " and 1 = 1 ";
    let sqlWhere4 = type.length > 0 ? " and type = '" + type + "' " : " and 1 = 1 ";
    var sqlWhere = " WHERE 1 = 1 " + sqlWhere1 + sqlWhere2 + sqlWhere3 + sqlWhere4;
    var sqlOrderBy = " order by created desc";
    var sqlPaging = " limit ?, ?"

    //get total records number
    await db_te.query('select count(*) as totalNum' + sqlTable + sqlWhere).then(function (rows) {
        result.totalNum = rows[0].totalNum;
        result.totalPage = getTotalPageNum(result.totalNum, pageSize);
    }).catch(function (err) {
    })
    console.log('select' + sqlFields + sqlTable + sqlWhere + sqlOrderBy + sqlPaging, params)
    await db_te.query('select' + sqlFields + sqlTable + sqlWhere + sqlOrderBy + sqlPaging, params).then(function (rows) {
        result.list = rows;

    }).catch(function (err) {
    })

    result.pageNum = pageNum;
    result.pageSize = pageSize;

    data.code = 0;
    data.message = 'SUCCESS';
    data.result = result;
    res.send(data);

});


function getFirstAndLastDayOfMonth(dateString) {
    const [year, month] = dateString.split('-');
    const firstDayOfMonth = new Date(year, month - 1, 1);
    const lastDayOfMonth = new Date(year, month, 0);

    return {
        firstDayOfMonth: formatDate(firstDayOfMonth),
        lastDayOfMonth: formatDate(lastDayOfMonth),
    };
}

function formatDate(date) {
    const year = date.getFullYear();
    const month = padNumber(date.getMonth() + 1);
    const day = padNumber(date.getDate());
    return `${year}-${month}-${day}`;
}

function padNumber(number) {
    return number.toString().padStart(2, '0');
}


router.get('/access-checker', async function (req, res, next) {

    if (!req.query.url) {
        res.status(400).json({ error: 'url is required' })
    } else {
        const results = await pa11y(req.query.url)
        res.status(200).json(results)
    }

});

function getTotalPageNum(totalRecord, pageSize) {
    return pageNum = parseInt((parseInt(totalRecord) + parseInt(pageSize) - 1) / parseInt(pageSize));
}

function removePoint(arr1, arr2) {
    for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
            if (arr2[i] == arr1[j]) {
                let index = arr1.indexOf(arr1[j]);
                arr1.splice(index, 1);
            }
        }
    }
    return arr1;
}

function convertUTCTimeToLocalTime(UTCDateString) {
    if (!UTCDateString) {
        return "-";
    }

    var date2 = new Date(UTCDateString); //这步是关键
    var year = date2.getFullYear();
    var mon = formatFunc(date2.getMonth() + 1);
    var day = formatFunc(date2.getDate());
    var hour = date2.getHours();
    var noon = hour >= 12 ? "PM" : "AM";
    hour = hour >= 12 ? hour - 12 : hour;
    hour = formatFunc(hour);
    var min = formatFunc(date2.getMinutes());
    var dateStr =
        year + "-" + mon + "-" + day + " " + noon + " " + hour + ":" + min;
    return dateStr;
}

function formatFunc(str) {
    //格式化显示
    return str > 9 ? str : "0" + str;
}

function getWeek(dt) {
    let d1 = new Date(dt);
    let d2 = new Date(dt);
    d2.setMonth(0);
    d2.setDate(1);
    let rq = d1 - d2;
    let days = Math.ceil(rq / (24 * 60 * 60 * 1000));
    let num = Math.ceil(days / 7);
    return num;
}

// get last day of a week
function getLastDayOfWeek(date) {
    if (isSunday(date)) {
        date = getPreviousDay(date);
    }
    var day = date.getDay();
    return new Date(date.getFullYear(), date.getMonth(), date.getDate() + (7 - day));
}

// get saturday number of a year
function getSaturdayNumber(date) {
    var d = new Date(date.getFullYear(), 0, 1);
    var dayOfYear = ((date - d) / 86400000) + 1;
    var saturday = Math.ceil(dayOfYear / 7);
    return saturday;
}


// get previous day of a date
function getPreviousDay(date) {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate() - 1);
}

// if date is sunday return true
function isSunday(date) {
    return date.getDay() === 0;
}

module.exports = router;