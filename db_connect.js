var env = process.env.NODE_ENV || 'prod';
var db = {};
var mysql = require('mysql');
console.log(env);
if (env == 'prod') {
    var pool = mysql.createPool({
        connectionLimit: 10,
        host: 'rm-j6c95u8ak3n7w3cc9wo.mysql.rds.aliyuncs.com',
        user: 'root',
        password: '5RGbetad^&CVDAZ',
        database: 'portal'
    });
} else if (env == 'dev-test') {
    var pool = mysql.createPool({
        connectionLimit: 10,
        host: '*************',
        user: 'root',
        password: 'BEabc123',
        database: 'portal'
    });
} else if (env == 'test') {
    var pool = mysql.createPool({
        connectionLimit: 10,
        host: '127.0.0.1',
        user: 'root',
        password: 'BEabc123',
        database: 'portal'
    });
} else if (env == 'dev-prod') {
    var pool = mysql.createPool({
        connectionLimit: 10,
        host: 'rm-j6c95u8ak3n7w3cc9wo.mysql.rds.aliyuncs.com',
        user: 'root',
        password: '5RGbetad^&CVDAZ',
        database: 'portal'
    });
}

db.query = function(sql, params) {

    return new Promise((resolve, reject)=>{
        pool.getConnection(function(err, connection) {
            if(err){
                //console.log("建立连接失败");
            } else {
                connection.query(sql, params, function(err, rows, fields) {
                    connection.release();
                    if(err) {
                        console.log("查询失败" + err);
                        connection.destroy();
                        resolve('error_exec');
                    } else {
                        connection.destroy();
                        console.log(rows.length + 'rows => ' + env);
                        resolve(rows);
                    }
                    
                })
            }
            //pool.end();
        });
    });

    
}


// db.query =  function(sql, params, callback) {

//     if (!sql) {
//         callback();
//         return;
//     }
//     pool.query(sql, params, function(err, rows, fields) {
//         if (err) {
//             console.log(err);
//             callback(err, null);
//             return;
//         };

//         callback(null, rows, fields);
//     });
// }
module.exports = db;